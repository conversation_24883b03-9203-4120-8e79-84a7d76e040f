// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"

	mock "github.com/stretchr/testify/mock"
)

// Calendar is an autogenerated mock type for the Calendar type
type Calendar struct {
	mock.Mock
}

// AddHoliday provides a mock function with given fields: ctx, req
func (_m *Calendar) AddHoliday(ctx context.Context, req *api.AddHolidayRequest) (*api.AddHolidayResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AddHoliday")
	}

	var r0 *api.AddHolidayResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.AddHolidayRequest) (*api.AddHolidayResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.AddHolidayRequest) *api.AddHolidayResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AddHolidayResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.AddHolidayRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AssignCalendarToElement provides a mock function with given fields: ctx, req
func (_m *Calendar) AssignCalendarToElement(ctx context.Context, req *api.AssignCalendarToElementRequest) (*api.AssignCalendarToElementResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AssignCalendarToElement")
	}

	var r0 *api.AssignCalendarToElementResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.AssignCalendarToElementRequest) (*api.AssignCalendarToElementResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.AssignCalendarToElementRequest) *api.AssignCalendarToElementResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AssignCalendarToElementResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.AssignCalendarToElementRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CalculateDeadline provides a mock function with given fields: ctx, req
func (_m *Calendar) CalculateDeadline(ctx context.Context, req *api.CalculateDeadlineRequest) (*api.CalculateDeadlineResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CalculateDeadline")
	}

	var r0 *api.CalculateDeadlineResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CalculateDeadlineRequest) (*api.CalculateDeadlineResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CalculateDeadlineRequest) *api.CalculateDeadlineResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CalculateDeadlineResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CalculateDeadlineRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateCalendar provides a mock function with given fields: ctx, req
func (_m *Calendar) CreateCalendar(ctx context.Context, req *api.CreateCalendarRequest) (*api.CreateCalendarResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateCalendar")
	}

	var r0 *api.CreateCalendarResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateCalendarRequest) (*api.CreateCalendarResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateCalendarRequest) *api.CreateCalendarResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateCalendarResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateCalendarRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteHoliday provides a mock function with given fields: ctx, req
func (_m *Calendar) DeleteHoliday(ctx context.Context, req *api.DeleteHolidayRequest) (*api.DeleteHolidayResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteHoliday")
	}

	var r0 *api.DeleteHolidayResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeleteHolidayRequest) (*api.DeleteHolidayResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.DeleteHolidayRequest) *api.DeleteHolidayResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.DeleteHolidayResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.DeleteHolidayRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCalendar provides a mock function with given fields: ctx, req
func (_m *Calendar) GetCalendar(ctx context.Context, req *api.GetCalendarRequest) (*api.GetCalendarResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCalendar")
	}

	var r0 *api.GetCalendarResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCalendarRequest) (*api.GetCalendarResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCalendarRequest) *api.GetCalendarResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCalendarResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCalendarRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCalendarList provides a mock function with given fields: ctx, req
func (_m *Calendar) GetCalendarList(ctx context.Context, req *api.GetCalendarListRequest) (*api.GetCalendarListResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCalendarList")
	}

	var r0 *api.GetCalendarListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCalendarListRequest) (*api.GetCalendarListResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCalendarListRequest) *api.GetCalendarListResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCalendarListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCalendarListRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetHolidays provides a mock function with given fields: ctx, req
func (_m *Calendar) GetHolidays(ctx context.Context, req *api.GetHolidaysRequest) (*api.GetHolidaysResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetHolidays")
	}

	var r0 *api.GetHolidaysResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetHolidaysRequest) (*api.GetHolidaysResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetHolidaysRequest) *api.GetHolidaysResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetHolidaysResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetHolidaysRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateCalendar provides a mock function with given fields: ctx, req
func (_m *Calendar) UpdateCalendar(ctx context.Context, req *api.UpdateCalendarRequest) (*api.UpdateCalendarResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCalendar")
	}

	var r0 *api.UpdateCalendarResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateCalendarRequest) (*api.UpdateCalendarResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateCalendarRequest) *api.UpdateCalendarResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateCalendarResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateCalendarRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateHoliday provides a mock function with given fields: ctx, req
func (_m *Calendar) UpdateHoliday(ctx context.Context, req *api.UpdateHolidayRequest) (*api.UpdateHolidayResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateHoliday")
	}

	var r0 *api.UpdateHolidayResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateHolidayRequest) (*api.UpdateHolidayResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateHolidayRequest) *api.UpdateHolidayResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateHolidayResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateHolidayRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpsertWorkingHours provides a mock function with given fields: ctx, req
func (_m *Calendar) UpsertWorkingHours(ctx context.Context, req *api.UpsertWorkingHoursRequest) (*api.UpsertWorkingHoursResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpsertWorkingHours")
	}

	var r0 *api.UpsertWorkingHoursResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpsertWorkingHoursRequest) (*api.UpsertWorkingHoursResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpsertWorkingHoursRequest) *api.UpsertWorkingHoursResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpsertWorkingHoursResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpsertWorkingHoursRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCalendar creates a new instance of Calendar. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCalendar(t interface {
	mock.TestingT
	Cleanup(func())
}) *Calendar {
	mock := &Calendar{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

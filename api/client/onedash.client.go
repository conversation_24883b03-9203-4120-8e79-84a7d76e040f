// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package client

import (
	bytes "bytes"
	context "context"
	fmt "fmt"
	_go "github.com/json-iterator/go"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
	http "net/http"
)

// OnedashClient makes calls to Onedash service.
type OnedashClient struct {
	machinery klient.RoundTripper
}

// MakeOnedashClient instantiates a new OnedashClient.
// Deprecated: Use NewOnedashClient instead
func MakeOnedashClient(initializer klient.Initializer) (*OnedashClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &OnedashClient{
		machinery: roundTripper,
	}, nil
}

// NewOnedashClient instantiates a new OnedashClient.
func NewOnedashClient(baseURL string, options ...klient.Option) (*OnedashClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &OnedashClient{
		machinery: roundTripper,
	}, nil
}

func (o *OnedashClient) CreateTicket(ctx context.Context, req *api.CreateTicketRequest) (*api.CreateTicketResponse, error) {
	reqShell := (*CreateTicketRequestShell)(req)
	resShell := &CreateTicketResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createTicketDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateTicketResponse)(resShell), err
}

func (o *OnedashClient) UpdateTicket(ctx context.Context, req *api.UpdateTicketRequest) (*api.UpdateTicketResponse, error) {
	reqShell := (*UpdateTicketRequestShell)(req)
	resShell := &UpdateTicketResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateTicketDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateTicketResponse)(resShell), err
}

func (o *OnedashClient) GetTicketList(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketListResponse, error) {
	reqShell := (*GetTicketListRequestShell)(req)
	resShell := &GetTicketListResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketListDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketListResponse)(resShell), err
}

func (o *OnedashClient) GetTicketExport(ctx context.Context, req *api.GetTicketListRequest) (*api.GetTicketExportResponse, error) {
	reqShell := (*GetTicketExportRequestShell)(req)
	resShell := &GetTicketExportResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketExportDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketExportResponse)(resShell), err
}

func (o *OnedashClient) GetTicketByID(ctx context.Context, req *api.GetTicketByIDRequest) (*api.GetTicketByIDResponse, error) {
	reqShell := (*GetTicketByIDRequestShell)(req)
	resShell := &GetTicketByIDResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketByIDDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketByIDResponse)(resShell), err
}

func (o *OnedashClient) CreateModule(ctx context.Context, req *api.CreateModuleRequest) (*api.CreateModuleResponse, error) {
	reqShell := (*CreateModuleRequestShell)(req)
	resShell := &CreateModuleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createModuleDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateModuleResponse)(resShell), err
}

func (o *OnedashClient) UpdateModule(ctx context.Context, req *api.UpdateModuleRequest) (*api.UpdateModuleResponse, error) {
	reqShell := (*UpdateModuleRequestShell)(req)
	resShell := &UpdateModuleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateModuleDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateModuleResponse)(resShell), err
}

func (o *OnedashClient) GetModules(ctx context.Context, req *api.GetModulesRequest) (*api.GetModulesResponse, error) {
	reqShell := (*GetModulesRequestShell)(req)
	resShell := &GetModulesResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getModulesDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetModulesResponse)(resShell), err
}

func (o *OnedashClient) CreateElement(ctx context.Context, req *api.CreateElementRequest) (*api.CreateElementResponse, error) {
	reqShell := (*CreateElementRequestShell)(req)
	resShell := &CreateElementResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createElementDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateElementResponse)(resShell), err
}

func (o *OnedashClient) UpdateElement(ctx context.Context, req *api.UpdateElementRequest) (*api.UpdateElementResponse, error) {
	reqShell := (*UpdateElementRequestShell)(req)
	resShell := &UpdateElementResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateElementDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateElementResponse)(resShell), err
}

func (o *OnedashClient) GetElements(ctx context.Context, req *api.GetElementsRequest) (*api.GetElementsResponse, error) {
	reqShell := (*GetElementsRequestShell)(req)
	resShell := &GetElementsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getElementsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetElementsResponse)(resShell), err
}

func (o *OnedashClient) GetElementByID(ctx context.Context, req *api.GetElementByIDRequest) (*api.GetElementByIDResponse, error) {
	reqShell := (*GetElementByIDRequestShell)(req)
	resShell := &GetElementByIDResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getElementByIDDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetElementByIDResponse)(resShell), err
}

func (o *OnedashClient) CreateTicketComment(ctx context.Context, req *api.CreateTicketCommentRequest) (*api.CreateTicketCommentResponse, error) {
	reqShell := (*CreateTicketCommentRequestShell)(req)
	resShell := &CreateTicketCommentResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createTicketCommentDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateTicketCommentResponse)(resShell), err
}

func (o *OnedashClient) GetTicketComments(ctx context.Context, req *api.GetTicketCommentsRequest) (*api.GetTicketCommentsResponse, error) {
	reqShell := (*GetTicketCommentsRequestShell)(req)
	resShell := &GetTicketCommentsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketCommentsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketCommentsResponse)(resShell), err
}

// LogAuditTrails: API to create Log Audit Trail
func (o *OnedashClient) LogAuditTrails(ctx context.Context, req *api.LogAuditTrailRequest) (*api.LogAuditTrailResponse, error) {
	reqShell := (*LogAuditTrailsRequestShell)(req)
	resShell := &LogAuditTrailsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &logAuditTrailsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.LogAuditTrailResponse)(resShell), err
}

func (o *OnedashClient) GetLogAuditTrails(ctx context.Context, req *api.GetLogAuditTrailsRequest) (*api.GetLogAuditTrailsResponse, error) {
	reqShell := (*GetLogAuditTrailsRequestShell)(req)
	resShell := &GetLogAuditTrailsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLogAuditTrailsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLogAuditTrailsResponse)(resShell), err
}

// UnlinkAccount: API to unlink account from partner
func (o *OnedashClient) UnlinkAccount(ctx context.Context, req *api.UnlinkAccountRequest) (*api.UnlinkAccountResponse, error) {
	reqShell := (*UnlinkAccountRequestShell)(req)
	resShell := &UnlinkAccountResponseShell{}
	clientCtx := klient.MakeContext(ctx, &unlinkAccountDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UnlinkAccountResponse)(resShell), err
}

// SendNotification is API to send notification email, push or push inbox
func (o *OnedashClient) SendNotification(ctx context.Context, req *api.SendNotificationRequest) (*api.SendNotificationResponse, error) {
	reqShell := (*SendNotificationRequestShell)(req)
	resShell := &SendNotificationResponseShell{}
	clientCtx := klient.MakeContext(ctx, &sendNotificationDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.SendNotificationResponse)(resShell), err
}

// TransferOnBehalf: API to transfer on behalf
func (o *OnedashClient) TransferOnBehalf(ctx context.Context, req *api.PaymentTransferRequest) (*api.PaymentTransferResponse, error) {
	reqShell := (*TransferOnBehalfRequestShell)(req)
	resShell := &TransferOnBehalfResponseShell{}
	clientCtx := klient.MakeContext(ctx, &transferOnBehalfDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.PaymentTransferResponse)(resShell), err
}

// BlockAccount is API to block account
func (o *OnedashClient) BlockAccount(ctx context.Context, req *api.BlockAccountRequest) (*api.BlockAccountResponse, error) {
	reqShell := (*BlockAccountRequestShell)(req)
	resShell := &BlockAccountResponseShell{}
	clientCtx := klient.MakeContext(ctx, &blockAccountDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.BlockAccountResponse)(resShell), err
}

// UnblockAccount is API to unblock account
func (o *OnedashClient) UnblockAccount(ctx context.Context, req *api.UnblockAccountRequest) (*api.UnblockAccountResponse, error) {
	reqShell := (*UnblockAccountRequestShell)(req)
	resShell := &UnblockAccountResponseShell{}
	clientCtx := klient.MakeContext(ctx, &unblockAccountDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UnblockAccountResponse)(resShell), err
}

// DeactivateLOC: API to deactivate LOC account
func (o *OnedashClient) DeactivateLOC(ctx context.Context, req *api.DeactivateLOCRequest) (*api.DeactivateLOCResponse, error) {
	reqShell := (*DeactivateLOCRequestShell)(req)
	resShell := &DeactivateLOCResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deactivateLOCDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.DeactivateLOCResponse)(resShell), err
}

// UpdateCASAAccountStatus: API to update the status of a casa account/bp asynchronously
func (o *OnedashClient) UpdateCASAAccountStatus(ctx context.Context, req *api.UpdateCASAAccountStatusRequest) (*api.UpdateCASAAccountStatusResponse, error) {
	reqShell := (*UpdateCASAAccountStatusRequestShell)(req)
	resShell := &UpdateCASAAccountStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateCASAAccountStatusDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateCASAAccountStatusResponse)(resShell), err
}

// CreateDocument is API to create document
func (o *OnedashClient) CreateDocument(ctx context.Context, req *api.CreateDocumentRequest) (*api.CreateDocumentResponse, error) {
	reqShell := (*CreateDocumentRequestShell)(req)
	resShell := &CreateDocumentResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createDocumentDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateDocumentResponse)(resShell), err
}

// DeleteDocument is API to delete document
func (o *OnedashClient) DeleteDocument(ctx context.Context, req *api.DeleteDocumentRequest) (*api.DeleteDocumentResponse, error) {
	reqShell := (*DeleteDocumentRequestShell)(req)
	resShell := &DeleteDocumentResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deleteDocumentDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.DeleteDocumentResponse)(resShell), err
}

// GetDocument is API to get document
func (o *OnedashClient) GetDocument(ctx context.Context, req *api.GetDocumentRequest) (*api.GetDocumentResponse, error) {
	reqShell := (*GetDocumentRequestShell)(req)
	resShell := &GetDocumentResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getDocumentDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDocumentResponse)(resShell), err
}

// GetTicketDocuments is api to get ticket documents by ticket id
func (o *OnedashClient) GetTicketDocuments(ctx context.Context, req *api.GetTicketDocumentsRequest) (*api.GetTicketDocumentsResponse, error) {
	reqShell := (*GetTicketDocumentsRequestShell)(req)
	resShell := &GetTicketDocumentsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketDocumentsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketDocumentsResponse)(resShell), err
}

// GetOptions is API for get dropdown option by type
func (o *OnedashClient) GetOptions(ctx context.Context, req *api.GetOptionsRequest) (*api.GetOptionsResponse, error) {
	reqShell := (*GetOptionsRequestShell)(req)
	resShell := &GetOptionsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getOptionsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetOptionsResponse)(resShell), err
}

// GetElementPriorities is API to get element priorities
func (o *OnedashClient) GetElementPriorities(ctx context.Context, req *api.GetElementPrioritiesRequest) (*api.GetElementPrioritiesResponse, error) {
	reqShell := (*GetElementPrioritiesRequestShell)(req)
	resShell := &GetElementPrioritiesResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getElementPrioritiesDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetElementPrioritiesResponse)(resShell), err
}

// GetStatuses is API to get statuses
func (o *OnedashClient) GetStatuses(ctx context.Context) (*api.GetStatusesResponse, error) {
	reqShell := (*GetStatusesRequestShell)(&struct{}{})
	resShell := &GetStatusesResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getStatusesDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetStatusesResponse)(resShell), err
}

// CreateStatus is API to create status
func (o *OnedashClient) CreateStatus(ctx context.Context, req *api.CreateStatusRequest) (*api.CreateStatusResponse, error) {
	reqShell := (*CreateStatusRequestShell)(req)
	resShell := &CreateStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createStatusDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateStatusResponse)(resShell), err
}

// UpdateStatus is API to update status
func (o *OnedashClient) UpdateStatus(ctx context.Context, req *api.UpdateStatusRequest) (*api.UpdateStatusResponse, error) {
	reqShell := (*UpdateStatusRequestShell)(req)
	resShell := &UpdateStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateStatusDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateStatusResponse)(resShell), err
}

// GetPriorities is API to get priorities
func (o *OnedashClient) GetPriorities(ctx context.Context, req *api.GetPrioritiesRequest) (*api.GetPrioritiesResponse, error) {
	reqShell := (*GetPrioritiesRequestShell)(req)
	resShell := &GetPrioritiesResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPrioritiesDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPrioritiesResponse)(resShell), err
}

// CreatePriority is API to create priority
func (o *OnedashClient) CreatePriority(ctx context.Context, req *api.CreatePriorityRequest) (*api.CreatePriorityResponse, error) {
	reqShell := (*CreatePriorityRequestShell)(req)
	resShell := &CreatePriorityResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createPriorityDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreatePriorityResponse)(resShell), err
}

// UpdatePriority is API to update priority
func (o *OnedashClient) UpdatePriority(ctx context.Context, req *api.UpdatePriorityRequest) (*api.UpdatePriorityResponse, error) {
	reqShell := (*UpdatePriorityRequestShell)(req)
	resShell := &UpdatePriorityResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updatePriorityDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdatePriorityResponse)(resShell), err
}

// GetTicketChains is API to get ticket chains
func (o *OnedashClient) GetTicketChains(ctx context.Context, req *api.GetTicketChainsRequest) (*api.GetTicketChainsResponse, error) {
	reqShell := (*GetTicketChainsRequestShell)(req)
	resShell := &GetTicketChainsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketChainsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketChainsResponse)(resShell), err
}

// UpdateTicketAssignee is API to update ticket assignee
func (o *OnedashClient) UpdateTicketAssignee(ctx context.Context, req *api.UpdateTicketAssigneeRequest) (*api.UpdateTicketAssigneeResponse, error) {
	reqShell := (*UpdateTicketAssigneeRequestShell)(req)
	resShell := &UpdateTicketAssigneeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateTicketAssigneeDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateTicketAssigneeResponse)(resShell), err
}

// CreateFeatureFlag is API to create feature flag
func (o *OnedashClient) CreateFeatureFlag(ctx context.Context, req *api.CreateFeatureFlagRequest) (*api.CreateFeatureFlagResponse, error) {
	reqShell := (*CreateFeatureFlagRequestShell)(req)
	resShell := &CreateFeatureFlagResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createFeatureFlagDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateFeatureFlagResponse)(resShell), err
}

// UpdateFeatureFlag is API to update feature flag
func (o *OnedashClient) UpdateFeatureFlag(ctx context.Context, req *api.UpdateFeatureFlagRequest) (*api.UpdateFeatureFlagResponse, error) {
	reqShell := (*UpdateFeatureFlagRequestShell)(req)
	resShell := &UpdateFeatureFlagResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateFeatureFlagDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateFeatureFlagResponse)(resShell), err
}

// GetFeatureFlag is API to get requested feature flag
func (o *OnedashClient) GetFeatureFlag(ctx context.Context, req *api.GetFeatureFlagRequest) (*api.GetFeatureFlagResponse, error) {
	reqShell := (*GetFeatureFlagRequestShell)(req)
	resShell := &GetFeatureFlagResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getFeatureFlagDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetFeatureFlagResponse)(resShell), err
}

// DeleteFeatureFlag is API to soft delete feature flag
func (o *OnedashClient) DeleteFeatureFlag(ctx context.Context, req *api.DeleteFeatureFlagRequest) (*api.DeleteFeatureFlagResponse, error) {
	reqShell := (*DeleteFeatureFlagRequestShell)(req)
	resShell := &DeleteFeatureFlagResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deleteFeatureFlagDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.DeleteFeatureFlagResponse)(resShell), err
}

// GetFeatureFlagList is API to get feature flag list
func (o *OnedashClient) GetFeatureFlagList(ctx context.Context, req *api.GetFeatureFlagListRequest) (*api.GetFeatureFlagListResponse, error) {
	reqShell := (*GetFeatureFlagListRequestShell)(req)
	resShell := &GetFeatureFlagListResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getFeatureFlagListDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetFeatureFlagListResponse)(resShell), err
}

// CustomerSearch: API to search customer details based on identifier
func (o *OnedashClient) CustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (*api.CustomerSearchResponse, error) {
	reqShell := (*CustomerSearchRequestShell)(req)
	resShell := &CustomerSearchResponseShell{}
	clientCtx := klient.MakeContext(ctx, &customerSearchDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CustomerSearchResponse)(resShell), err
}

// GetCustomers: API to search multiple customer based on identifier
func (o *OnedashClient) GetCustomers(ctx context.Context, req *api.GetCustomersRequest) (*api.GetCustomersResponse, error) {
	reqShell := (*GetCustomersRequestShell)(req)
	resShell := &GetCustomersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCustomersDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCustomersResponse)(resShell), err
}

// GetRolesDataSegregation is get roles list API for data segregation purpose
func (o *OnedashClient) GetRolesDataSegregation(ctx context.Context, req *api.GetDataSegregationRoleListRequest) (*api.GetDataSegregationRoleListResponse, error) {
	reqShell := (*GetRolesDataSegregationRequestShell)(req)
	resShell := &GetRolesDataSegregationResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getRolesDataSegregationDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDataSegregationRoleListResponse)(resShell), err
}

// GetDataSegregation is API for get data segregation detail for specific role
func (o *OnedashClient) GetDataSegregation(ctx context.Context, req *api.GetDataSegregationRequest) (*api.GetDataSegregationResponse, error) {
	reqShell := (*GetDataSegregationRequestShell)(req)
	resShell := &GetDataSegregationResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getDataSegregationDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDataSegregationResponse)(resShell), err
}

// UpdateDataSegregation is API for update data segregation
func (o *OnedashClient) UpdateDataSegregation(ctx context.Context, req *api.UpdateDataSegregationRequest) (*api.UpdateDataSegregationResponse, error) {
	reqShell := (*UpdateDataSegregationRequestShell)(req)
	resShell := &UpdateDataSegregationResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateDataSegregationDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateDataSegregationResponse)(resShell), err
}

// GetCustomersDataPoints is API to get specific data points
func (o *OnedashClient) GetCustomersDataPoint(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	reqShell := (*GetCustomersDataPointRequestShell)(req)
	resShell := &GetCustomersDataPointResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCustomersDataPointDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CustomerSearchDataPointResponse)(resShell), err
}

// GetCustomerSegements is API to get all customer segemnts
func (o *OnedashClient) GetCustomerSegements(ctx context.Context) (*api.GetCustomerSegmentsResponse, error) {
	reqShell := (*GetCustomerSegementsRequestShell)(&struct{}{})
	resShell := &GetCustomerSegementsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCustomerSegementsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCustomerSegmentsResponse)(resShell), err
}

// GetTicketRequestors is API to get all ticket requestors
func (o *OnedashClient) GetTicketRequestors(ctx context.Context) (*api.GetTicketRequestorsResponse, error) {
	reqShell := (*GetTicketRequestorsRequestShell)(&struct{}{})
	resShell := &GetTicketRequestorsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketRequestorsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketRequestorsResponse)(resShell), err
}

// GetCustomerAccounts is API to get specific customer and list of account
func (o *OnedashClient) GetCustomerAccounts(ctx context.Context, req *api.GetCustomerAccountRequest) (*api.GetCustomerAccountsResponse, error) {
	reqShell := (*GetCustomerAccountsRequestShell)(req)
	resShell := &GetCustomerAccountsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCustomerAccountsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCustomerAccountsResponse)(resShell), err
}

// GetTicketFields is API to get ticket fields configuration
func (o *OnedashClient) GetTicketFields(ctx context.Context, req *api.GetTicketFieldsRequest) (*api.GetTicketFieldsResponse, error) {
	reqShell := (*GetTicketFieldsRequestShell)(req)
	resShell := &GetTicketFieldsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTicketFieldsDescriptor)
	err := o.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTicketFieldsResponse)(resShell), err
}

// CreateTicketRequestShell is a wrapper to make the object a klient.Request
type CreateTicketRequestShell api.CreateTicketRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateTicketRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateTicketResponseShell is a wrapper to make the object a klient.Request
type CreateTicketResponseShell api.CreateTicketResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateTicketResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateTicketRequestShell is a wrapper to make the object a klient.Request
type UpdateTicketRequestShell api.UpdateTicketRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateTicketRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket/" + fmt.Sprint(u.Id)
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateTicketResponseShell is a wrapper to make the object a klient.Request
type UpdateTicketResponseShell api.UpdateTicketResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateTicketResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetTicketListRequestShell is a wrapper to make the object a klient.Request
type GetTicketListRequestShell api.GetTicketListRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketListRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetTicketListResponseShell is a wrapper to make the object a klient.Request
type GetTicketListResponseShell api.GetTicketListResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketListResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetTicketExportRequestShell is a wrapper to make the object a klient.Request
type GetTicketExportRequestShell api.GetTicketListRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketExportRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/export/ticket"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetTicketExportResponseShell is a wrapper to make the object a klient.Request
type GetTicketExportResponseShell api.GetTicketExportResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketExportResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetTicketByIDRequestShell is a wrapper to make the object a klient.Request
type GetTicketByIDRequestShell api.GetTicketByIDRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketByIDRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetTicketByIDResponseShell is a wrapper to make the object a klient.Request
type GetTicketByIDResponseShell api.GetTicketByIDResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketByIDResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateModuleRequestShell is a wrapper to make the object a klient.Request
type CreateModuleRequestShell api.CreateModuleRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateModuleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/modules"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateModuleResponseShell is a wrapper to make the object a klient.Request
type CreateModuleResponseShell api.CreateModuleResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateModuleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateModuleRequestShell is a wrapper to make the object a klient.Request
type UpdateModuleRequestShell api.UpdateModuleRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateModuleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/modules/" + fmt.Sprint(u.Id)
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateModuleResponseShell is a wrapper to make the object a klient.Request
type UpdateModuleResponseShell api.UpdateModuleResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateModuleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetModulesRequestShell is a wrapper to make the object a klient.Request
type GetModulesRequestShell api.GetModulesRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetModulesRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/modules"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetModulesResponseShell is a wrapper to make the object a klient.Request
type GetModulesResponseShell api.GetModulesResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetModulesResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateElementRequestShell is a wrapper to make the object a klient.Request
type CreateElementRequestShell api.CreateElementRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateElementRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/elements"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateElementResponseShell is a wrapper to make the object a klient.Request
type CreateElementResponseShell api.CreateElementResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateElementResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateElementRequestShell is a wrapper to make the object a klient.Request
type UpdateElementRequestShell api.UpdateElementRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateElementRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/elements/" + fmt.Sprint(u.Id)
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateElementResponseShell is a wrapper to make the object a klient.Request
type UpdateElementResponseShell api.UpdateElementResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateElementResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetElementsRequestShell is a wrapper to make the object a klient.Request
type GetElementsRequestShell api.GetElementsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetElementsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/elements"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetElementsResponseShell is a wrapper to make the object a klient.Request
type GetElementsResponseShell api.GetElementsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetElementsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetElementByIDRequestShell is a wrapper to make the object a klient.Request
type GetElementByIDRequestShell api.GetElementByIDRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetElementByIDRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/elements/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetElementByIDResponseShell is a wrapper to make the object a klient.Request
type GetElementByIDResponseShell api.GetElementByIDResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetElementByIDResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateTicketCommentRequestShell is a wrapper to make the object a klient.Request
type CreateTicketCommentRequestShell api.CreateTicketCommentRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateTicketCommentRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket/" + fmt.Sprint(c.Id) + "/comments"
	fullURL := baseURL + filledPath

	pathVar0 := c.Id
	c.Id = 0

	defer func() {
		c.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateTicketCommentResponseShell is a wrapper to make the object a klient.Request
type CreateTicketCommentResponseShell api.CreateTicketCommentResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateTicketCommentResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetTicketCommentsRequestShell is a wrapper to make the object a klient.Request
type GetTicketCommentsRequestShell api.GetTicketCommentsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketCommentsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket/" + fmt.Sprint(g.Id) + "/comments"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetTicketCommentsResponseShell is a wrapper to make the object a klient.Request
type GetTicketCommentsResponseShell api.GetTicketCommentsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketCommentsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// LogAuditTrailsRequestShell is a wrapper to make the object a klient.Request
type LogAuditTrailsRequestShell api.LogAuditTrailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *LogAuditTrailsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/log-audit-trails"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// LogAuditTrailsResponseShell is a wrapper to make the object a klient.Request
type LogAuditTrailsResponseShell api.LogAuditTrailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *LogAuditTrailsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// GetLogAuditTrailsRequestShell is a wrapper to make the object a klient.Request
type GetLogAuditTrailsRequestShell api.GetLogAuditTrailsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLogAuditTrailsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/get-log-audit-trails"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLogAuditTrailsResponseShell is a wrapper to make the object a klient.Request
type GetLogAuditTrailsResponseShell api.GetLogAuditTrailsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLogAuditTrailsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UnlinkAccountRequestShell is a wrapper to make the object a klient.Request
type UnlinkAccountRequestShell api.UnlinkAccountRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UnlinkAccountRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/unlink-account"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UnlinkAccountResponseShell is a wrapper to make the object a klient.Request
type UnlinkAccountResponseShell api.UnlinkAccountResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UnlinkAccountResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// SendNotificationRequestShell is a wrapper to make the object a klient.Request
type SendNotificationRequestShell api.SendNotificationRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (s *SendNotificationRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/send-notification"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(s)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// SendNotificationResponseShell is a wrapper to make the object a klient.Request
type SendNotificationResponseShell api.SendNotificationResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (s *SendNotificationResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(s)
}

// TransferOnBehalfRequestShell is a wrapper to make the object a klient.Request
type TransferOnBehalfRequestShell api.PaymentTransferRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (t *TransferOnBehalfRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/transfer-on-behalf"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(t)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// TransferOnBehalfResponseShell is a wrapper to make the object a klient.Request
type TransferOnBehalfResponseShell api.PaymentTransferResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (t *TransferOnBehalfResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(t)
}

// BlockAccountRequestShell is a wrapper to make the object a klient.Request
type BlockAccountRequestShell api.BlockAccountRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (b *BlockAccountRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/block-account"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(b)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// BlockAccountResponseShell is a wrapper to make the object a klient.Request
type BlockAccountResponseShell api.BlockAccountResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (b *BlockAccountResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(b)
}

// UnblockAccountRequestShell is a wrapper to make the object a klient.Request
type UnblockAccountRequestShell api.UnblockAccountRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UnblockAccountRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/unblock-account"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UnblockAccountResponseShell is a wrapper to make the object a klient.Request
type UnblockAccountResponseShell api.UnblockAccountResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UnblockAccountResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// DeactivateLOCRequestShell is a wrapper to make the object a klient.Request
type DeactivateLOCRequestShell api.DeactivateLOCRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (d *DeactivateLOCRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/deactivate-loc"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(d)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// DeactivateLOCResponseShell is a wrapper to make the object a klient.Request
type DeactivateLOCResponseShell api.DeactivateLOCResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (d *DeactivateLOCResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(d)
}

// UpdateCASAAccountStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateCASAAccountStatusRequestShell api.UpdateCASAAccountStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateCASAAccountStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/update-casa-account/status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateCASAAccountStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateCASAAccountStatusResponseShell api.UpdateCASAAccountStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateCASAAccountStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateDocumentRequestShell is a wrapper to make the object a klient.Request
type CreateDocumentRequestShell api.CreateDocumentRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateDocumentRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/documents"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateDocumentResponseShell is a wrapper to make the object a klient.Request
type CreateDocumentResponseShell api.CreateDocumentResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateDocumentResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// DeleteDocumentRequestShell is a wrapper to make the object a klient.Request
type DeleteDocumentRequestShell api.DeleteDocumentRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (d *DeleteDocumentRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/documents/" + fmt.Sprint(d.Name)
	fullURL := baseURL + filledPath

	pathVar0 := d.Name
	d.Name = ""

	defer func() {
		d.Name = pathVar0
	}()

	jsonBytes, err := _go.Marshal(d)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("DELETE", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// DeleteDocumentResponseShell is a wrapper to make the object a klient.Request
type DeleteDocumentResponseShell api.DeleteDocumentResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (d *DeleteDocumentResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(d)
}

// GetDocumentRequestShell is a wrapper to make the object a klient.Request
type GetDocumentRequestShell api.GetDocumentRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetDocumentRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/documents/" + fmt.Sprint(g.Name)
	fullURL := baseURL + filledPath

	pathVar0 := g.Name
	g.Name = ""

	defer func() {
		g.Name = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetDocumentResponseShell is a wrapper to make the object a klient.Request
type GetDocumentResponseShell api.GetDocumentResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetDocumentResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetTicketDocumentsRequestShell is a wrapper to make the object a klient.Request
type GetTicketDocumentsRequestShell api.GetTicketDocumentsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketDocumentsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket/" + fmt.Sprint(g.Id) + "/documents"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetTicketDocumentsResponseShell is a wrapper to make the object a klient.Request
type GetTicketDocumentsResponseShell api.GetTicketDocumentsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketDocumentsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetOptionsRequestShell is a wrapper to make the object a klient.Request
type GetOptionsRequestShell api.GetOptionsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetOptionsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/options/" + fmt.Sprint(g.Type)
	fullURL := baseURL + filledPath

	pathVar0 := g.Type
	g.Type = ""

	defer func() {
		g.Type = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetOptionsResponseShell is a wrapper to make the object a klient.Request
type GetOptionsResponseShell api.GetOptionsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetOptionsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetElementPrioritiesRequestShell is a wrapper to make the object a klient.Request
type GetElementPrioritiesRequestShell api.GetElementPrioritiesRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetElementPrioritiesRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/elements/" + fmt.Sprint(g.Id) + "/priorities"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetElementPrioritiesResponseShell is a wrapper to make the object a klient.Request
type GetElementPrioritiesResponseShell api.GetElementPrioritiesResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetElementPrioritiesResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetStatusesRequestShell is a wrapper to make the object a klient.Request
type GetStatusesRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetStatusesRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/statuses"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetStatusesResponseShell is a wrapper to make the object a klient.Request
type GetStatusesResponseShell api.GetStatusesResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetStatusesResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateStatusRequestShell is a wrapper to make the object a klient.Request
type CreateStatusRequestShell api.CreateStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/statuses"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateStatusResponseShell is a wrapper to make the object a klient.Request
type CreateStatusResponseShell api.CreateStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateStatusRequestShell api.UpdateStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/statuses/" + fmt.Sprint(u.Id)
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateStatusResponseShell api.UpdateStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetPrioritiesRequestShell is a wrapper to make the object a klient.Request
type GetPrioritiesRequestShell api.GetPrioritiesRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPrioritiesRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/priorities"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetPrioritiesResponseShell is a wrapper to make the object a klient.Request
type GetPrioritiesResponseShell api.GetPrioritiesResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPrioritiesResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreatePriorityRequestShell is a wrapper to make the object a klient.Request
type CreatePriorityRequestShell api.CreatePriorityRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreatePriorityRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/priorities"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreatePriorityResponseShell is a wrapper to make the object a klient.Request
type CreatePriorityResponseShell api.CreatePriorityResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreatePriorityResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdatePriorityRequestShell is a wrapper to make the object a klient.Request
type UpdatePriorityRequestShell api.UpdatePriorityRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdatePriorityRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/priorities/" + fmt.Sprint(u.Id)
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdatePriorityResponseShell is a wrapper to make the object a klient.Request
type UpdatePriorityResponseShell api.UpdatePriorityResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdatePriorityResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetTicketChainsRequestShell is a wrapper to make the object a klient.Request
type GetTicketChainsRequestShell api.GetTicketChainsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketChainsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket-chains"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetTicketChainsResponseShell is a wrapper to make the object a klient.Request
type GetTicketChainsResponseShell api.GetTicketChainsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketChainsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateTicketAssigneeRequestShell is a wrapper to make the object a klient.Request
type UpdateTicketAssigneeRequestShell api.UpdateTicketAssigneeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateTicketAssigneeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket/" + fmt.Sprint(u.Id) + "/assignee"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateTicketAssigneeResponseShell is a wrapper to make the object a klient.Request
type UpdateTicketAssigneeResponseShell api.UpdateTicketAssigneeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateTicketAssigneeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateFeatureFlagRequestShell is a wrapper to make the object a klient.Request
type CreateFeatureFlagRequestShell api.CreateFeatureFlagRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateFeatureFlagRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/feature-flag"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateFeatureFlagResponseShell is a wrapper to make the object a klient.Request
type CreateFeatureFlagResponseShell api.CreateFeatureFlagResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateFeatureFlagResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateFeatureFlagRequestShell is a wrapper to make the object a klient.Request
type UpdateFeatureFlagRequestShell api.UpdateFeatureFlagRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateFeatureFlagRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/feature-flag"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateFeatureFlagResponseShell is a wrapper to make the object a klient.Request
type UpdateFeatureFlagResponseShell api.UpdateFeatureFlagResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateFeatureFlagResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetFeatureFlagRequestShell is a wrapper to make the object a klient.Request
type GetFeatureFlagRequestShell api.GetFeatureFlagRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetFeatureFlagRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/feature-flag"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetFeatureFlagResponseShell is a wrapper to make the object a klient.Request
type GetFeatureFlagResponseShell api.GetFeatureFlagResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetFeatureFlagResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// DeleteFeatureFlagRequestShell is a wrapper to make the object a klient.Request
type DeleteFeatureFlagRequestShell api.DeleteFeatureFlagRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (d *DeleteFeatureFlagRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/feature-flag"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(d)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("DELETE", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// DeleteFeatureFlagResponseShell is a wrapper to make the object a klient.Request
type DeleteFeatureFlagResponseShell api.DeleteFeatureFlagResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (d *DeleteFeatureFlagResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(d)
}

// GetFeatureFlagListRequestShell is a wrapper to make the object a klient.Request
type GetFeatureFlagListRequestShell api.GetFeatureFlagListRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetFeatureFlagListRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/feature-flag/list"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetFeatureFlagListResponseShell is a wrapper to make the object a klient.Request
type GetFeatureFlagListResponseShell api.GetFeatureFlagListResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetFeatureFlagListResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CustomerSearchRequestShell is a wrapper to make the object a klient.Request
type CustomerSearchRequestShell api.CustomerSearchRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CustomerSearchRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/customer/search"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CustomerSearchResponseShell is a wrapper to make the object a klient.Request
type CustomerSearchResponseShell api.CustomerSearchResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CustomerSearchResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetCustomersRequestShell is a wrapper to make the object a klient.Request
type GetCustomersRequestShell api.GetCustomersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCustomersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/customers/search"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCustomersResponseShell is a wrapper to make the object a klient.Request
type GetCustomersResponseShell api.GetCustomersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCustomersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetRolesDataSegregationRequestShell is a wrapper to make the object a klient.Request
type GetRolesDataSegregationRequestShell api.GetDataSegregationRoleListRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetRolesDataSegregationRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/data-segregation/roles"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetRolesDataSegregationResponseShell is a wrapper to make the object a klient.Request
type GetRolesDataSegregationResponseShell api.GetDataSegregationRoleListResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetRolesDataSegregationResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetDataSegregationRequestShell is a wrapper to make the object a klient.Request
type GetDataSegregationRequestShell api.GetDataSegregationRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetDataSegregationRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/data-segregation"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetDataSegregationResponseShell is a wrapper to make the object a klient.Request
type GetDataSegregationResponseShell api.GetDataSegregationResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetDataSegregationResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateDataSegregationRequestShell is a wrapper to make the object a klient.Request
type UpdateDataSegregationRequestShell api.UpdateDataSegregationRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateDataSegregationRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/data-segregation"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateDataSegregationResponseShell is a wrapper to make the object a klient.Request
type UpdateDataSegregationResponseShell api.UpdateDataSegregationResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateDataSegregationResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetCustomersDataPointRequestShell is a wrapper to make the object a klient.Request
type GetCustomersDataPointRequestShell api.CustomerSearchDataPointRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCustomersDataPointRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/customers/search/data-point"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCustomersDataPointResponseShell is a wrapper to make the object a klient.Request
type GetCustomersDataPointResponseShell api.CustomerSearchDataPointResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCustomersDataPointResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCustomerSegementsRequestShell is a wrapper to make the object a klient.Request
type GetCustomerSegementsRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCustomerSegementsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/customer-segments"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCustomerSegementsResponseShell is a wrapper to make the object a klient.Request
type GetCustomerSegementsResponseShell api.GetCustomerSegmentsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCustomerSegementsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetTicketRequestorsRequestShell is a wrapper to make the object a klient.Request
type GetTicketRequestorsRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketRequestorsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket-requestors"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetTicketRequestorsResponseShell is a wrapper to make the object a klient.Request
type GetTicketRequestorsResponseShell api.GetTicketRequestorsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketRequestorsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCustomerAccountsRequestShell is a wrapper to make the object a klient.Request
type GetCustomerAccountsRequestShell api.GetCustomerAccountRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCustomerAccountsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/customers/search/accounts"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetCustomerAccountsResponseShell is a wrapper to make the object a klient.Request
type GetCustomerAccountsResponseShell api.GetCustomerAccountsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCustomerAccountsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetTicketFieldsRequestShell is a wrapper to make the object a klient.Request
type GetTicketFieldsRequestShell api.GetTicketFieldsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTicketFieldsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/ticket-fields"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetTicketFieldsResponseShell is a wrapper to make the object a klient.Request
type GetTicketFieldsResponseShell api.GetTicketFieldsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTicketFieldsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

var createTicketDescriptor = klient.EndpointDescriptor{
	Name:        "CreateTicket",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/ticket",
}

var updateTicketDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateTicket",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/ticket/{id}",
}

var getTicketListDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketList",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/ticket",
}

var getTicketExportDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketExport",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/export/ticket",
}

var getTicketByIDDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketByID",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/ticket/{id}",
}

var createModuleDescriptor = klient.EndpointDescriptor{
	Name:        "CreateModule",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/modules",
}

var updateModuleDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateModule",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/modules/{id}",
}

var getModulesDescriptor = klient.EndpointDescriptor{
	Name:        "GetModules",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/modules",
}

var createElementDescriptor = klient.EndpointDescriptor{
	Name:        "CreateElement",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/elements",
}

var updateElementDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateElement",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/elements/{id}",
}

var getElementsDescriptor = klient.EndpointDescriptor{
	Name:        "GetElements",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/elements",
}

var getElementByIDDescriptor = klient.EndpointDescriptor{
	Name:        "GetElementByID",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/elements/{id}",
}

var createTicketCommentDescriptor = klient.EndpointDescriptor{
	Name:        "CreateTicketComment",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/ticket/{id}/comments",
}

var getTicketCommentsDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketComments",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/ticket/{id}/comments",
}

var logAuditTrailsDescriptor = klient.EndpointDescriptor{
	Name:        "LogAuditTrails",
	Description: "LogAuditTrails: API to create Log Audit Trail",
	Method:      "POST",
	Path:        "/api/v1/log-audit-trails",
}

var getLogAuditTrailsDescriptor = klient.EndpointDescriptor{
	Name:        "GetLogAuditTrails",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/get-log-audit-trails",
}

var unlinkAccountDescriptor = klient.EndpointDescriptor{
	Name:        "UnlinkAccount",
	Description: "UnlinkAccount: API to unlink account from partner",
	Method:      "POST",
	Path:        "/api/v1/unlink-account",
}

var sendNotificationDescriptor = klient.EndpointDescriptor{
	Name:        "SendNotification",
	Description: "SendNotification is API to send notification email, push or push inbox",
	Method:      "POST",
	Path:        "/api/v1/send-notification",
}

var transferOnBehalfDescriptor = klient.EndpointDescriptor{
	Name:        "TransferOnBehalf",
	Description: "TransferOnBehalf: API to transfer on behalf",
	Method:      "POST",
	Path:        "/api/v1/transfer-on-behalf",
}

var blockAccountDescriptor = klient.EndpointDescriptor{
	Name:        "BlockAccount",
	Description: "BlockAccount is API to block account",
	Method:      "POST",
	Path:        "/api/v1/block-account",
}

var unblockAccountDescriptor = klient.EndpointDescriptor{
	Name:        "UnblockAccount",
	Description: "UnblockAccount is API to unblock account",
	Method:      "POST",
	Path:        "/api/v1/unblock-account",
}

var deactivateLOCDescriptor = klient.EndpointDescriptor{
	Name:        "DeactivateLOC",
	Description: "DeactivateLOC: API to deactivate LOC account",
	Method:      "POST",
	Path:        "/api/v1/deactivate-loc",
}

var updateCASAAccountStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateCASAAccountStatus",
	Description: "UpdateCASAAccountStatus: API to update the status of a casa account/bp asynchronously",
	Method:      "POST",
	Path:        "/api/v1/update-casa-account/status",
}

var createDocumentDescriptor = klient.EndpointDescriptor{
	Name:        "CreateDocument",
	Description: "CreateDocument is API to create document",
	Method:      "POST",
	Path:        "/api/v1/documents",
}

var deleteDocumentDescriptor = klient.EndpointDescriptor{
	Name:        "DeleteDocument",
	Description: "DeleteDocument is API to delete document",
	Method:      "DELETE",
	Path:        "/api/v1/documents/{name}",
}

var getDocumentDescriptor = klient.EndpointDescriptor{
	Name:        "GetDocument",
	Description: "GetDocument is API to get document",
	Method:      "GET",
	Path:        "/api/v1/documents/{name}",
}

var getTicketDocumentsDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketDocuments",
	Description: "GetTicketDocuments is api to get ticket documents by ticket id",
	Method:      "GET",
	Path:        "/api/v1/ticket/{id}/documents",
}

var getOptionsDescriptor = klient.EndpointDescriptor{
	Name:        "GetOptions",
	Description: "GetOptions is API for get dropdown option by type",
	Method:      "GET",
	Path:        "/api/v1/options/{type}",
}

var getElementPrioritiesDescriptor = klient.EndpointDescriptor{
	Name:        "GetElementPriorities",
	Description: "GetElementPriorities is API to get element priorities",
	Method:      "GET",
	Path:        "/api/v1/elements/{id}/priorities",
}

var getStatusesDescriptor = klient.EndpointDescriptor{
	Name:        "GetStatuses",
	Description: "GetStatuses is API to get statuses",
	Method:      "GET",
	Path:        "/api/v1/statuses",
}

var createStatusDescriptor = klient.EndpointDescriptor{
	Name:        "CreateStatus",
	Description: "CreateStatus is API to create status",
	Method:      "POST",
	Path:        "/api/v1/statuses",
}

var updateStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateStatus",
	Description: "UpdateStatus is API to update status",
	Method:      "PUT",
	Path:        "/api/v1/statuses/{id}",
}

var getPrioritiesDescriptor = klient.EndpointDescriptor{
	Name:        "GetPriorities",
	Description: "GetPriorities is API to get priorities",
	Method:      "GET",
	Path:        "/api/v1/priorities",
}

var createPriorityDescriptor = klient.EndpointDescriptor{
	Name:        "CreatePriority",
	Description: "CreatePriority is API to create priority",
	Method:      "POST",
	Path:        "/api/v1/priorities",
}

var updatePriorityDescriptor = klient.EndpointDescriptor{
	Name:        "UpdatePriority",
	Description: "UpdatePriority is API to update priority",
	Method:      "PUT",
	Path:        "/api/v1/priorities/{id}",
}

var getTicketChainsDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketChains",
	Description: "GetTicketChains is API to get ticket chains",
	Method:      "GET",
	Path:        "/api/v1/ticket-chains",
}

var updateTicketAssigneeDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateTicketAssignee",
	Description: "UpdateTicketAssignee is API to update ticket assignee",
	Method:      "PUT",
	Path:        "/api/v1/ticket/{id}/assignee",
}

var createFeatureFlagDescriptor = klient.EndpointDescriptor{
	Name:        "CreateFeatureFlag",
	Description: "CreateFeatureFlag is API to create feature flag",
	Method:      "POST",
	Path:        "/api/v1/feature-flag",
}

var updateFeatureFlagDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateFeatureFlag",
	Description: "UpdateFeatureFlag is API to update feature flag",
	Method:      "PUT",
	Path:        "/api/v1/feature-flag",
}

var getFeatureFlagDescriptor = klient.EndpointDescriptor{
	Name:        "GetFeatureFlag",
	Description: "GetFeatureFlag is API to get requested feature flag",
	Method:      "GET",
	Path:        "/api/v1/feature-flag",
}

var deleteFeatureFlagDescriptor = klient.EndpointDescriptor{
	Name:        "DeleteFeatureFlag",
	Description: "DeleteFeatureFlag is API to soft delete feature flag",
	Method:      "DELETE",
	Path:        "/api/v1/feature-flag",
}

var getFeatureFlagListDescriptor = klient.EndpointDescriptor{
	Name:        "GetFeatureFlagList",
	Description: "GetFeatureFlagList is API to get feature flag list",
	Method:      "POST",
	Path:        "/api/v1/feature-flag/list",
}

var customerSearchDescriptor = klient.EndpointDescriptor{
	Name:        "CustomerSearch",
	Description: "CustomerSearch: API to search customer details based on identifier",
	Method:      "POST",
	Path:        "/api/v1/customer/search",
}

var getCustomersDescriptor = klient.EndpointDescriptor{
	Name:        "GetCustomers",
	Description: "GetCustomers: API to search multiple customer based on identifier",
	Method:      "GET",
	Path:        "/api/v1/customers/search",
}

var getRolesDataSegregationDescriptor = klient.EndpointDescriptor{
	Name:        "GetRolesDataSegregation",
	Description: "GetRolesDataSegregation is get roles list API for data segregation purpose",
	Method:      "POST",
	Path:        "/api/v1/data-segregation/roles",
}

var getDataSegregationDescriptor = klient.EndpointDescriptor{
	Name:        "GetDataSegregation",
	Description: "GetDataSegregation is API for get data segregation detail for specific role",
	Method:      "POST",
	Path:        "/api/v1/data-segregation",
}

var updateDataSegregationDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateDataSegregation",
	Description: "UpdateDataSegregation is API for update data segregation",
	Method:      "PUT",
	Path:        "/api/v1/data-segregation",
}

var getCustomersDataPointDescriptor = klient.EndpointDescriptor{
	Name:        "GetCustomersDataPoint",
	Description: "GetCustomersDataPoints is API to get specific data points",
	Method:      "POST",
	Path:        "/api/v1/customers/search/data-point",
}

var getCustomerSegementsDescriptor = klient.EndpointDescriptor{
	Name:        "GetCustomerSegements",
	Description: "GetCustomerSegements is API to get all customer segemnts",
	Method:      "GET",
	Path:        "/api/v1/customer-segments",
}

var getTicketRequestorsDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketRequestors",
	Description: "GetTicketRequestors is API to get all ticket requestors",
	Method:      "GET",
	Path:        "/api/v1/ticket-requestors",
}

var getCustomerAccountsDescriptor = klient.EndpointDescriptor{
	Name:        "GetCustomerAccounts",
	Description: "GetCustomerAccounts is API to get specific customer and list of account",
	Method:      "POST",
	Path:        "/api/v1/customers/search/accounts",
}

var getTicketFieldsDescriptor = klient.EndpointDescriptor{
	Name:        "GetTicketFields",
	Description: "GetTicketFields is API to get ticket fields configuration",
	Method:      "GET",
	Path:        "/api/v1/ticket-fields",
}

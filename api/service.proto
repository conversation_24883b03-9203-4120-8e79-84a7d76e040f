syntax = "proto3";

package onedash;

option go_package = "gitlab.super-id.net/bersama/opsce/onedash-be/api";

import "gxs/api/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";

enum TicketSource {
    DEFAULT = 0;
    ONEDASH = 1;
    APPIAN = 2;
    CRM = 3;
    AML_SERVICE = 4;
}

message Document {
    int64 id = 1;
    string name = 2;
    string url = 3;
    int64 ticketID = 4;
    string createdAt = 5;
    string updatedAt = 6;
    string createdBy = 7;
    string updatedBy = 8;
    string description = 9;
    string type = 10;
}

message TicketData {
    google.protobuf.Any payload = 1;
    string description = 2;
    repeated Document documents = 3;
    google.protobuf.Any capture = 4;
    google.protobuf.Any preview = 5;
}

// CreateTicketRequest ...
message CreateTicketRequest {
    TicketData data = 1;
    int64 elementID = 2;
    int64 priorityID = 3;
    TicketSource source = 4;
    string action = 5;
    string note = 6;
    int64 ticketRequestorID = 7;
    string caseCategory = 8;
    string caseSubcategory = 9;
    string domainID = 10;
    string channel = 11;
    int64 statusID = 12;
    repeated int64 documentIDs = 13;
    int64 parentTicketId = 14;
}

// CreateTicketResponse ...
message CreateTicketResponse {
    int64 id = 1;
}

// UpdateTicketRequest ...
message UpdateTicketRequest {
    int64 id = 1 [(gxs.api.validate) = "required,gt=0"];
    TicketData data = 2;
    int64 nextStatusID = 3 [(gxs.api.validate) = "required,gt=0"];
    string action = 4 [(gxs.api.validate) = "required"];
    string note = 5;
    bool isUpdateData = 6;
    string targetUserID = 7;
    // Fields for hold/resume actions
    string holdReason = 8 [(gxs.api.validate) = "max=100"];
    string holdRemarks = 9 [(gxs.api.validate) = "max=100"];
}

// UpdateTicketResponse ...
message UpdateTicketResponse {
    int64 id = 1;
}

// UpdateTicketAssigneeRequest ...
message UpdateTicketAssigneeRequest {
    int64 id = 1;
    string targetUserID = 2;
}

// UpdateTicketAssigneeResponse ...
message UpdateTicketAssigneeResponse {
    int64 id = 1;
}

message GetTicketListRequest {
    string searchKey = 1;
    int64 offset = 2 [(gxs.api.validate) = "offset"];
    int64 limit = 3 [(gxs.api.validate) = "limit"];
    int64 elementID = 4;
    int64 statusID = 5;
    string assigneeUserID = 6;
    int64 moduleID = 7;
    string caseType = 8;
    string caseSubcategory = 9;
    int64 priorityID = 10;
    string createdDateStart = 11;
    string createdDateEnd = 12;
    string dueDateStart = 13;
    string dueDateEnd = 14;
    string closedDateStart = 15;
    string closedDateEnd = 16;
    string actionedUserID = 17;
    string sourceSystem = 18;
    string channel = 19;
    string customerSegmentName = 20;
    Sort sortBy = 21 [(gxs.api.validate) = "omitempty,ticket_sort"];
    string createdOn = 22;
    string deadline = 23;
    repeated string Column = 24;
}

message GetTicketListResponse {
    repeated Ticket tickets = 1;
    int64 count = 2;
    int64 offset = 3;
}

message GetTicketExportResponse {
    string dataExport = 1;
}

message Ticket {
    int64 id = 1;
    int64 elementID = 2;
    int64 priorityID = 3;
    string createdAt = 4;
    string updatedAt = 5;
    string createdBy = 6;
    string updatedBy = 7;
    TicketData data = 8 [(gxs.api.validate) = "required"];
    int64 statusID = 9;
    TicketSource source = 10;
    string deadlineTime = 11;
    int64 assigneeUserID = 12;
    string assigneeUserName = 13;
    string elementName = 14;
    string priorityName = 15;
    string ticketRequestorName = 16;
    string caseCategory = 17;
    string caseSubcategory = 18;
    string domainID = 19;
    string channel = 20;
    string customerSegmentName = 21;
    string ticketCloseDatetime = 22;
    repeated string actionedUsers = 23;
    int64 parentTicketID = 24;
    string statusName = 25;
    string moduleName = 26;
    string holdReason = 27;
    string holdRemarks = 28;
    string onHoldAt = 29;
    string resumedAt = 30;
    int64 totalHoldDurationSec = 31;
    string originalDeadlineTime = 32;
}

message TicketHistory {
    int64 id = 1;
    int64 createdBy = 2 [(gxs.api.validate) = "required,gt=0"];
    string createdAt = 3 [(gxs.api.validate) = "required"];
    string action = 4 [(gxs.api.validate) = "required,max=255"];
    string note = 5 [(gxs.api.validate) = "max=255"];
    int64 ticketID = 6 [(gxs.api.validate) = "required,gt=0"];
    TicketData data = 7;
    int64 prevStatusID = 8 [(gxs.api.validate) = "required,gt=0"];
    int64 nextStatusID = 9 [(gxs.api.validate) = "required,gt=0"];
}

message LinkedTicket{
    int64 id = 1;
    string createdAt = 2;
    string createdBy = 3;
    int64 statusID = 4;
    int64 elementID = 5;
    string caseCategory = 6;
    string caseSubcategory = 7;
    int64 parentTicketID = 8;
    string relationship = 9;
}

message GetTicketByIDRequest {
    int64 id = 1;
}

message GetTicketByIDResponse {
    Ticket ticket = 1;
    repeated TicketHistory histories = 2;
    map<string, int64> actionsNextStatusMap = 3;
    repeated LinkedTicket linkedTickets = 4;
    repeated TicketComment comments = 5;
    repeated string rolesRequired = 6;
}

message CreateModuleRequest {
    string name = 1;
    int32 status = 2;
    bool hasTicketing = 3;
}

message CreateModuleResponse {
    int64 id = 1;
}

message UpdateModuleRequest {
    int64 id = 1;
    string name = 2;
    int32 status = 3;
    bool hasTicketing = 4;
}

message UpdateModuleResponse {
    int64 id = 1;
}

message GetModulesRequest {
    string searchKey = 1;
    int64 offset = 2 [(gxs.api.validate) = "offset"];
    int64 limit = 3 [(gxs.api.validate) = "limit"];
    string name = 4;
    bool hasTicketing = 5;
}

message GetModulesResponse {
    repeated Module modules = 1;
    int64 count = 2;
    int64 offset = 3;
}

message Module {
    int64 id = 1;
    string name = 2;
    string createdAt = 3;
    string updatedAt = 4;
    string createdBy = 5;
    string updatedBy = 6;
    int32 status = 7 [(gxs.api.noomit) = true];
    bool hasTicketing = 8;
}

message CreateElementRequest {
    string name = 1;
    int64 moduleID = 2;
    string code = 3;
    int64 defaultPriorityID = 4;
    int32 status = 5;
    bool hasTicketing = 6;
    repeated TicketChain ticketChains = 7;
    int64 defaultCustomerSegmentID = 8;
    int64 defaultTicketRequestorID = 9;
}

message CreateElementResponse {
    int64 id = 1;
}

message UpdateElementRequest {
    int64 id = 1;
    string name = 2;
    int64 moduleID = 3;
    string code = 4;
    int64 defaultPriorityID = 5;
    int32 status = 6;
    bool hasTicketing = 7;
    repeated TicketChain ticketChains = 8;
    int64 defaultCustomerSegmentID = 9;
    int64 defaultTicketRequestorID = 10;
}

message UpdateElementResponse {
    int64 id = 1;
}

message GetElementsRequest {
    int64 moduleID = 1;
    string searchKey = 2;
    int64 offset = 3 [(gxs.api.validate) = "offset"];
    int64 limit = 4 [(gxs.api.validate) = "limit"];
    bool hasTicketing = 5;
}

message GetElementsResponse {
    repeated Element elements = 1;
    int64 count = 2;
    int64 offset = 3;
}

message GetElementByIDRequest {
    int64 id = 1;
}

message GetElementByIDResponse {
    Element element = 1;
}

message Element {
    int64 id = 1;
    string name = 2;
    string code = 3;
    int64 moduleID = 4;
    int64 defaultPriorityID = 5;
    string createdAt = 6;
    string updatedAt = 7;
    string createdBy = 8;
    string updatedBy = 9;
    int32 status = 10 [(gxs.api.noomit) = true];
    bool hasTicketing = 11;
    int64 defaultCustomerSegmentID = 12;
    int64 defaultTicketRequestorID = 13;
}

message CreateTicketCommentRequest {
    int64 id = 1;
    string comment = 2;
}

message CreateTicketCommentResponse {
    int64 id = 1;
}

message GetTicketCommentsRequest {
    int64 id = 1;
}

message GetTicketCommentsResponse {
    repeated TicketComment comments = 1;
}

message TicketComment {
    int64 id = 1;
    string comment = 2;
    string action = 3;
    string createdAt = 4;
    int64 createdBy = 5;
    string createdByUserName = 6;
}

message LogAuditTrailRequest{
    string userID = 1;
    string name = 2;
    string event = 3;
    string email = 4;
    string action = 5;
    string relatedID = 6;
    google.protobuf.Any metadata = 7;
    string serviceName = 8;
    string safeID = 9;
}

message LogAuditTrailResponse{
    string Status = 1;
}

message LogAuditTrail{
    int64 ID = 1;
    string name = 2;
    string userID = 3;
    string email = 4;
    string event = 5;
    string action = 6;
    google.protobuf.Any metadata = 7;
    string relatedID = 8;
    string service = 9;
    google.protobuf.Timestamp actionTime = 10;
    string safeID = 11;
}

message GetLogAuditTrailsRequest{
    string userID = 1;
    string startTime = 2;
    string endTime = 3;
    string relatedID = 4;
    string event = 5;
    string action = 6;
    int64 limit = 7;
    string startingBefore = 8;
    string endingAfter = 9;
    string serviceName = 10;
    string safeID = 11;
}

message GetLogAuditTrailsResponse{
    map<string, string> Links = 1;
    repeated LogAuditTrail logs = 2;
}

// SendNotificationType
enum SendNotificationType{
    EMAIL = 0;
    PUSH = 1;
    PUSH_INBOX = 2;
}

message EmailAttachment {
    string filename = 1;
    bytes file = 2;
    string url = 3;
}

message Template {
    string id = 1;
    string language = 2;
    map<string, string> params = 3;
}

// SendNotificationRequest ...
message SendNotificationRequest {
    string recipientID = 1;
    Template template = 2;
    repeated EmailAttachment attachments = 3;
    SendNotificationType notificationType = 4;
}
// SendNotificationResponse
message SendNotificationResponse {
    string messageID = 1;
}

// UnlinkAccountRequest
message UnlinkAccountRequest{
    string billingAgreementID = 1;
    string safeID = 2;
    string actionBy = 3;
    bool skipNotification = 4;
    string ticketID = 5;
}

// UnlinkAccountResponse
message UnlinkAccountResponse{
    string status = 1;
}

message AccountDetail {
    string pairingID = 1;
    string number = 2;
    string swiftCode = 3;
    string displayName = 4;
    string bankCode = 5;
    string accountAddress = 6;
    bool isGLAccount = 7;
}

// PaymentRailRPP
message PaymentRailRPP {
    string purposeCode = 1;
}

// PaymentRailFast
message PaymentRailFAST {
    string purposeCode = 1;
}

// PaymentTransferRequest
message PaymentTransferRequest {
    string transferType = 1;
    int64 amount = 2;
    string currency = 3;
    AccountDetail sourceAccount = 4;
    AccountDetail destinationAccount = 5;
    string remarks = 6;
    string idempotencyKey = 7;
    oneof properties {
        PaymentRailFAST fast = 8;
        PaymentRailRPP rpp = 9;
    }
    string transactionDomain = 10;
    string transactionType = 11;
    string transactionSubType = 12;
    string networkID = 13;
    int64 fee = 14;
    bool sendNotification = 15;
    string ticketID = 16;
}

// PaymentTransferResponse
message PaymentTransferResponse {
    string status = 1;
}

// BlockAccountRequest ...
message BlockAccountRequest {
    string AccountID = 1 [(gxs.api.validate) = "required,account_id"];
    string IdempotencyKey = 2 [(gxs.api.validate) = "required"];
    string UpdatedBy = 3;
    string TicketID = 4;
    string IsSendNotification = 5;
    repeated string HoldCodes = 6;
}

// BlockAccountResponse ...
message BlockAccountResponse {
    string AccountID = 1;
    string Status = 2;
    string FailureReason = 3;
}

// UnblockAccountRequest ...
message UnblockAccountRequest {
    string AccountID = 1 [(gxs.api.validate) = "required,account_id"];
    string IdempotencyKey = 2 [(gxs.api.validate) = "required"];
    string UpdatedBy = 3;
    string TicketID = 4;
    string IsSendNotification = 5;
    repeated string HoldCodes = 6;
}

// UnblockAccountResponse ...
message UnblockAccountResponse {
    string AccountID = 1;
    string Status = 2;
    string FailureReason = 3;
}

message DeactivateLOCRequest{
    string idempotencyKey = 1;
    string safeID = 2;
    string locAccountID = 3;
    map<string,string> metadata = 4;
    string reasonCode = 5;
    string createdBy = 6;
    string ticketID = 7;
}

message DeactivateLOCResponse{
    string status = 1;
}

enum AccountStatus {
    ACTIVE = 0;
    DORMANT = 1;
    CLOSED = 2;
}

// UpdateCASAAccountStatusRequest
message UpdateCASAAccountStatusRequest {
    string accountID = 1;
    AccountStatus status = 2;
    google.protobuf.Timestamp closingTimestamp = 3;
    string idempotencyKey = 4;
    string updatedBy = 5;
    string ticketID = 6;
}

// UpdateCASAAccountStatusResponse
message UpdateCASAAccountStatusResponse {
    string accountID = 1;
    AccountStatus status = 2;
    string updatedBy = 3;
}

message Filter {
    string column = 1;
    repeated google.protobuf.Any value = 2;
}

enum SortOrder {
    ASC = 0;
    DESC = 1;
}

message Sort {
    string column = 1;
    SortOrder sort = 2;
}

enum OptionType {
    roles = 0;
    elements = 1;
    permissions = 2;
    modules = 3;
    reasons = 4;
    ticketFilter = 5;
    actionedUsers = 6;
}

message TicketFilterOptions {
    repeated Options modules = 1;
    repeated Options elements = 2;
    repeated Options sourceSystem = 3;
    repeated Options integrationStatus = 4;
    repeated Options assignTo = 5;
}

message GetOptionsRequest {
    OptionType type = 1 [(gxs.api.validate) = "required,oneof=roles elements permissions modules reasons ticketFilter actionedUsers"];
}

message GetOptionsResponse {
    google.protobuf.Any data = 1;
}

message Options {
    int64 id = 1;
    string name = 2;
    int64 moduleId = 3;
    string key = 4;
}

message GetElementPrioritiesRequest {
    int64 id = 1;
}

message GetElementPrioritiesResponse {
    repeated Priority priorities = 1;
}

message Priority {
    int64 id = 1;
    string name = 2;
    int64 timeToResolveSec = 3;
    string createdAt = 4;
    string updatedAt = 5;
    int64 createdBy = 6;
    int64 updatedBy = 7;
    int64 elementID = 8;
}

message GetStatusesRequest {}

message GetStatusesResponse {
    repeated Status statuses = 1;
}

message CreateStatusRequest {
    string name = 1 [(gxs.api.validate) = "required"];
}

message CreateStatusResponse {
    int64 id = 1;
}

message UpdateStatusRequest {
    int64 id = 1 [(gxs.api.validate) = "required"];
    string name = 2 [(gxs.api.validate) = "required"];
}

message UpdateStatusResponse {
    int64 id = 1;
}

message GetPrioritiesRequest {
    int64 elementID = 1;
}

message GetPrioritiesResponse {
    repeated Priority priorities = 1;
    int64 count = 2;
    int64 offset = 3;
}

message CreatePriorityRequest {
    string name = 1 [(gxs.api.validate) = "required"];
    int64 timeToResolveSec = 2 [(gxs.api.validate) = "required"];
    int64 elementID = 3 [(gxs.api.validate) = "required"];
}

message CreatePriorityResponse {
    int64 id = 1;
}

message UpdatePriorityRequest {
    int64 id = 1 [(gxs.api.validate) = "required"];
    string name = 2 [(gxs.api.validate) = "required"];
    int64 timeToResolveSec = 3 [(gxs.api.validate) = "required"];
    int64 elementID = 4 [(gxs.api.validate) = "required"];
}

message UpdatePriorityResponse {
    int64 id = 1;
}

message GetTicketChainsRequest {
    int64 elementID = 1;
}

message GetTicketChainsResponse {
    repeated TicketChain chains = 1;
}

message TicketChain {
    int64 id = 1;
    string name = 2;
    string createdAt = 3;
    string updatedAt = 4;
    string createdBy = 5;
    string updatedBy = 6;
    int64 elementID = 7;
    int64 prevStatusID = 8;
    int64 nextStatusID = 9;
    repeated int64 permissionsIDs = 10;
    string actionName = 11;
    repeated Condition conditions = 12;
}

message Condition {
    string type = 1;
    string operator = 2;
    google.protobuf.Any value = 3;
    repeated int64 rolesIDs = 4;
}


message Status {
    int64 id = 1;
    string name = 2;
}

message CreateFeatureFlagRequest {
    string name = 1 [(gxs.api.validate) = "required"];
    int32 value = 2;
    string description = 3;
}

message CreateFeatureFlagResponse {
    string status = 1;
}

message UpdateFeatureFlagRequest {
    string name = 1 [(gxs.api.validate) = "required"];
    int32 value = 2;
    string description = 3;
}

message UpdateFeatureFlagResponse {
    string status = 1;
}

message GetFeatureFlagRequest {
    repeated string name = 1 [(gxs.api.validate) = "required"];
}

message GetFeatureFlagResponse {
    repeated FeatureFlag data = 1;
}

message FeatureFlag {
    string name = 1;
    int32 value = 2;
    string description = 3;
    string createdAt = 4;
    string updatedAt = 5;
    string createdBy = 6;
    string updatedBy = 7;
}

message DeleteFeatureFlagRequest {
    string name = 1 [(gxs.api.validate) = "required"];
}

message DeleteFeatureFlagResponse {
    string status = 1;
}

message GetFeatureFlagListRequest {
    string searchKey = 1;
    int64 offset = 2 [(gxs.api.validate) = "offset"];
    int64 limit = 3 [(gxs.api.validate) = "limit"];
    repeated Filter filter = 4 [(gxs.api.validate) = "omitempty,ff_filter"];
    Sort sortBy = 5 [(gxs.api.validate) = "omitempty,ff_sort"];
}

message GetFeatureFlagListResponse {
    int64 count = 1;
    int64 offset = 2;
    repeated FeatureFlag data = 3;
}

// CreateDocumentRequest ...
message CreateDocumentRequest {
    string name = 1 [(gxs.api.validate) = "required,max=255"];
    string payload = 2;
    int64 ticketID = 3;
    string description = 4;
    string type = 5;
}

// CreateDocumentResponse ...
message CreateDocumentResponse {
    int64 id = 1;
    string url = 2;
    string name = 3;
    string presignUrl = 4;
}

// DeleteDocumentRequest ...
message DeleteDocumentRequest {
    string name = 1;
}

// DeleteDocumentResponse ...
message DeleteDocumentResponse {
    int64 id = 1;
}

// GetDocumentRequest ...
message GetDocumentRequest {
    string name = 1;
}

// GetDocumentResponse ...
message GetDocumentResponse {
    int64 id = 1;
    string payload = 2;
    string presignUrl = 3;
}

// GetTicketDocumentsRequest ...
message GetTicketDocumentsRequest {
    int64 id = 1;
}

// GetTicketDocumentsResponse ...
message GetTicketDocumentsResponse {
    repeated Document documents = 1;
}

// GetTicketDocumentRequest ...
message GetTicketDocumentRequest {
    int64 id = 1;
}

message CustomerSearchRequest {
    string identifier = 1;
    IdentifierType identifierType = 2 [(gxs.api.validate) = "identifier_type_value"];
    string key = 3;
    map<string, string> payload = 4;
}

message CustomerSearchResponse {
    string status = 1;
    CustomerSearchStructure structure = 2;
    map<string, google.protobuf.Any> data = 3;
}

message CustomerSearchStructure {
    string key = 1;
    string type = 2;
    string label = 3;
    repeated Structure children = 4;
}

message Structure {
    string key = 1;
    string type = 2;
    string label = 3;
    map<string, string> payload = 4;
    bool hasChildren = 5;
    repeated Structure children = 6;
}

enum IdentifierType {
    CIF = 0;
    ID_NUMBER = 1;
    PHONE_NUMBER = 2;
    SAFE_ID = 3;
    NAME = 4;
    ACCOUNT_NUMBER = 5;
}

message GetCustomersRequest {
    string identifier = 1 [(gxs.api.validate) = "required"];
    IdentifierType identifierType = 2 [(gxs.api.validate) = "required"];
    int64 page = 3 [(gxs.api.validate) = "required"];
}

message GetCustomersResponse {
    bool isLastPage = 1;
    google.protobuf.ListValue customers = 2;
}

message GetDataSegregationRoleListRequest {
    string searchKey = 1;
    int64 offset = 2 [(gxs.api.validate) = "offset"];
    int64 limit = 3 [(gxs.api.validate) = "limit"];
    Sort sortBy = 4 [(gxs.api.validate) = "omitempty,data_segregation_sort"];
}

message GetDataSegregationRoleListResponse {
    int64 count = 1;
    int64 offset = 2;
    repeated DataSegregationRole data = 3;
}

message DataSegregationRole {
    int64 id = 1;
    string name = 2;
}

message GetDataSegregationRequest {
    int64 roleID = 1 [(gxs.api.validate) = "required,gt=0"];
    int64 offset = 2 [(gxs.api.validate) = "offset"];
    int64 limit = 3 [(gxs.api.validate) = "limit"];
    string searchKey = 4;
    int64 parentID = 5;
    Sort sortBy = 6 [(gxs.api.validate) = "omitempty,data_segregation_sort"];
}

message GetDataSegregationResponse {
    int64 offset = 1;
    bool isLastPage = 2;
    repeated DataSegregation data = 3;
}

message DataSegregation {
    int64 id = 1;
    string name = 2;
    int64 parentID = 3;
    int64 status = 4;
    bool hasChild = 5;
    string parentName = 6;
}

message UpdateDataSegregationRequest {
    int64 roleID = 1 [(gxs.api.validate) = "required,gt=0"];
    repeated int64 segregationIDs = 2;
    int64 status = 3 [(gxs.api.validate) = "binary"];
    bool isCheckParent = 4;
}

message UpdateDataSegregationResponse {
    string status = 1;
}

message CustomerSearchDataPointRequest {
    string key = 1 [(gxs.api.validate) = "required"];
    map<string, string> payload = 2;
}

message CustomerSearchDataPointResponse {
    google.protobuf.Any data = 1;
}

enum LogType {
    LOGIN = 0;
    INTERACTION = 1;
    ACTIVITY = 2;
    FACIALANDLIVENESS = 3;
    ECOSYSTEM = 4;
    MFA = 5;
    TRANSACTIONLIMITCHANGESHISTORY = 6;
    MARKETINGNOTIFICATIONPREFERENCE = 7;
    OVONABUNG = 8;
    AGGREGATEMFA = 9;
    RESETFMLC = 10;
}

message GetCustomerAccountRequest {
    string identifier = 1 [(gxs.api.validate) = "required"];
    IdentifierType identifierType = 2 [(gxs.api.validate) = "required"];
    repeated string data = 3 [
        (gxs.api.validate) = "required,oneof=customer_info customer_status accounts"
    ];
}

message GetCustomerAccountsResponse {
    bool isLastPage = 1;
    string customer_status = 2;
    google.protobuf.ListValue customers = 3;
    google.protobuf.ListValue accounts = 4;
}

message Pagination {
    string nextCursorID = 1;
    string prevCursorID = 2;
}

// GetEventLogRequest
message GetEventLogRequest {
    string safeID = 1;
    string endDate = 2;
    string startDate = 3;
    int64 pageSize = 4;
    string startingBefore = 5;
    string endingAfter = 6;
    LogType logType = 7;
    string partnerID = 8;
}

// GetEventLogResponse
message GetEventLogResponse {
    Pagination pagination = 1;
    google.protobuf.Any data = 2;
}

message GetCustomerSegmentsResponse {
    repeated CustomerSegment customerSegments = 1;
}

message CustomerSegment {
    int64 id = 1;
    string createdBy = 2;
    string createdAt = 3;
    string updatedBy = 4;
    string updatedAt = 5;
    string name = 6;
    string isActive = 7;
}

message GetTicketRequestorsResponse {
    repeated TicketRequestor ticketRequestors = 1;
}

message TicketRequestor {
    int64 id = 1;
    string createdBy = 2;
    string createdAt = 3;
    string updatedBy = 4;
    string updatedAt = 5;
    string name = 6;
    string isActive = 7;
}

// Ticket field option for dropdown fields
message TicketFieldOption {
    string value = 1;
    string label = 2;
}

// Ticket Hold Code field option for dropdown fields
message TicketHoldCodeFieldOption {
    string value = 1;
    string label = 2;
    bool mandatory_internal_watchlist_checkbox = 3 [(gxs.api.noomit) = true];
}

// Ticket field definition
message TicketField {
    string field_id = 1;
    string field_name = 2;
    string field_type = 3;
    bool is_required = 4 [(gxs.api.noomit) = true];
    string is_required_dependancy = 5;
    string default_value = 6;
    bool readonly = 7;
    google.protobuf.Any options = 8;
}

// GetTicketFieldsRequest - request to get ticket fields configuration
message GetTicketFieldsRequest {
    int64 moduleID = 1;
    int64 elementID = 2;
}

// GetTicketFieldsResponse - response containing ticket fields configuration
message GetTicketFieldsResponse {
    repeated TicketField fields = 1;
}

// Onedash is the service that provides ticket management.
service Onedash {

    rpc CreateTicket(CreateTicketRequest) returns (CreateTicketResponse) {
        option (google.api.http) = {
            post: "/api/v1/ticket",
            body: "*",
        };
    }

    rpc UpdateTicket(UpdateTicketRequest) returns (UpdateTicketResponse) {
        option (google.api.http) = {
            put: "/api/v1/ticket/{id}",
            body: "*",
        };
    }

    rpc GetTicketList(GetTicketListRequest) returns (GetTicketListResponse) {
        option (google.api.http) = {
            get: "/api/v1/ticket",
        };
    }

    rpc GetTicketExport(GetTicketListRequest) returns (GetTicketExportResponse) {
        option (google.api.http) = {
            post: "/api/v1/export/ticket",
            body: "*"
        };
    }

    rpc GetTicketByID(GetTicketByIDRequest) returns (GetTicketByIDResponse) {
        option (google.api.http) = {
            get: "/api/v1/ticket/{id}",
        };
    }

    rpc CreateModule(CreateModuleRequest) returns (CreateModuleResponse) {
        option (google.api.http) = {
            post: "/api/v1/modules",
            body: "*",
        };
    }

    rpc UpdateModule(UpdateModuleRequest) returns (UpdateModuleResponse) {
        option (google.api.http) = {
            put: "/api/v1/modules/{id}",
            body: "*",
        };
    }

    rpc GetModules(GetModulesRequest) returns (GetModulesResponse) {
        option (google.api.http) = {
            get: "/api/v1/modules",
        };
    }

    rpc CreateElement(CreateElementRequest) returns (CreateElementResponse) {
        option (google.api.http) = {
            post: "/api/v1/elements",
            body: "*",
        };
    }

    rpc UpdateElement(UpdateElementRequest) returns (UpdateElementResponse) {
        option (google.api.http) = {
            put: "/api/v1/elements/{id}",
            body: "*",
        };
    }

    rpc GetElements(GetElementsRequest) returns (GetElementsResponse) {
        option (google.api.http) = {
            get: "/api/v1/elements",
        };
    }

    rpc GetElementByID(GetElementByIDRequest) returns (GetElementByIDResponse) {
        option (google.api.http) = {
            get: "/api/v1/elements/{id}",
        };
    }

    rpc CreateTicketComment(CreateTicketCommentRequest) returns (CreateTicketCommentResponse) {
        option (google.api.http) = {
            post: "/api/v1/ticket/{id}/comments",
            body: "*",
        };
    }

    rpc GetTicketComments(GetTicketCommentsRequest) returns (GetTicketCommentsResponse) {
        option (google.api.http) = {
            get: "/api/v1/ticket/{id}/comments",
        };
    }


    // LogAuditTrails: API to create Log Audit Trail
    rpc LogAuditTrails (LogAuditTrailRequest) returns (LogAuditTrailResponse){
        option (google.api.http) = {
            post : "/api/v1/log-audit-trails",
            body : "*",
        };
//        option (gxs.api.auth) = {
//            client_identities: ["servicename.SentryPartnerT6"]
//        };
    }

    rpc GetLogAuditTrails (GetLogAuditTrailsRequest) returns (GetLogAuditTrailsResponse){
        option (google.api.http) = {
            post : "/api/v1/get-log-audit-trails",
            body : "*",
        };
//        option (gxs.api.auth) = {
//            client_identities: ["servicename.SentryPartnerT6"]
//        };
    }

    // UnlinkAccount: API to unlink account from partner
    rpc UnlinkAccount(UnlinkAccountRequest) returns (UnlinkAccountResponse){
        option (google.api.http) = {
            post: "/api/v1/unlink-account",
            body: "*",
        };
//        option (gxs.api.auth) = {
//            client_identities: ["servicename.SentryPartnerT6"]
//        };
    }

    // SendNotification is API to send notification email, push or push inbox
    rpc SendNotification (SendNotificationRequest) returns (SendNotificationResponse){
        option (google.api.http) = {
            post : "/api/v1/send-notification",
            body : "*",
        };
        //        option (gxs.api.auth) = {
        //            client_identities: ["servicename.SentryPartnerT6"]
        //        };
    }

    // TransferOnBehalf: API to transfer on behalf
    rpc TransferOnBehalf(PaymentTransferRequest) returns (PaymentTransferResponse){
        option (google.api.http) = {
            post: "/api/v1/transfer-on-behalf",
            body: "*",
        };
        //        option (gxs.api.auth) = {
        //            client_identities: ["servicename.SentryPartnerT6"]
        //        };
    }

    // BlockAccount is API to block account
    rpc BlockAccount (BlockAccountRequest) returns (BlockAccountResponse){
        option (google.api.http) = {
            post : "/api/v1/block-account",
            body : "*",
        };
    }

    // UnblockAccount is API to unblock account
    rpc UnblockAccount (UnblockAccountRequest) returns (UnblockAccountResponse){
        option (google.api.http) = {
            post : "/api/v1/unblock-account",
            body : "*",
        };
    }

    // DeactivateLOC: API to deactivate LOC account
    rpc DeactivateLOC(DeactivateLOCRequest) returns (DeactivateLOCResponse){
        option (google.api.http) = {
            post: "/api/v1/deactivate-loc",
            body: "*",
        };
//        option (gxs.api.auth) = {
//            client_identities: ["servicename.SentryPartnerT6"]
//        };
    }

    // UpdateCASAAccountStatus: API to update the status of a casa account/bp asynchronously
    rpc UpdateCASAAccountStatus(UpdateCASAAccountStatusRequest) returns (UpdateCASAAccountStatusResponse){
        option(google.api.http) = {
            post:"/api/v1/update-casa-account/status",
            body:"*",
        };
        //        option (gxs.api.auth) = {
        //            client_identities: ["servicename.SentryPartnerT6"]
        //        };
    }

    // CreateDocument is API to create document
    rpc CreateDocument(CreateDocumentRequest) returns (CreateDocumentResponse) {
        option (google.api.http) = {
            post: "/api/v1/documents",
            body: "*",
        };
    }

    // DeleteDocument is API to delete document
    rpc DeleteDocument(DeleteDocumentRequest) returns (DeleteDocumentResponse) {
        option (google.api.http) = {
            delete: "/api/v1/documents/{name}",
            body: "*",
        };
    }

    // GetDocument is API to get document
    rpc GetDocument(GetDocumentRequest) returns (GetDocumentResponse) {
        option (google.api.http) = {
            get: "/api/v1/documents/{name}",
            body: "*",
        };
    }

    // GetTicketDocuments is api to get ticket documents by ticket id
    rpc GetTicketDocuments(GetTicketDocumentsRequest) returns (GetTicketDocumentsResponse) {
        option (google.api.http) = {
            get: "/api/v1/ticket/{id}/documents",
        };
    }

    // GetOptions is API for get dropdown option by type
    rpc GetOptions(GetOptionsRequest) returns (GetOptionsResponse) {
        option (google.api.http) = {
            get: "/api/v1/options/{type}",
            body: "*",
        };
    }
    // GetElementPriorities is API to get element priorities
    rpc GetElementPriorities(GetElementPrioritiesRequest) returns (GetElementPrioritiesResponse) {
        option (google.api.http) = {
            get: "/api/v1/elements/{id}/priorities",
        };
    }

    // GetStatuses is API to get statuses
    rpc GetStatuses(GetStatusesRequest) returns (GetStatusesResponse) {
        option (google.api.http) = {
            get: "/api/v1/statuses",
        };
    }

    // CreateStatus is API to create status
    rpc CreateStatus(CreateStatusRequest) returns (CreateStatusResponse) {
        option (google.api.http) = {
            post: "/api/v1/statuses",
            body: "*",
        };
    }

    // UpdateStatus is API to update status
    rpc UpdateStatus(UpdateStatusRequest) returns (UpdateStatusResponse) {
        option (google.api.http) = {
            put: "/api/v1/statuses/{id}",
            body: "*",
        };
    }


    // GetPriorities is API to get priorities
    rpc GetPriorities(GetPrioritiesRequest) returns (GetPrioritiesResponse) {
        option (google.api.http) = {
            get: "/api/v1/priorities",
        };
    }

    // CreatePriority is API to create priority
    rpc CreatePriority(CreatePriorityRequest) returns (CreatePriorityResponse) {
        option (google.api.http) = {
            post: "/api/v1/priorities",
            body: "*",
        };
    }

    // UpdatePriority is API to update priority
    rpc UpdatePriority(UpdatePriorityRequest) returns (UpdatePriorityResponse) {
        option (google.api.http) = {
            put: "/api/v1/priorities/{id}",
            body: "*",
        };
    }

    // GetTicketChains is API to get ticket chains
    rpc GetTicketChains(GetTicketChainsRequest) returns (GetTicketChainsResponse) {
        option (google.api.http) = {
            get: "/api/v1/ticket-chains",
            body: "*",
        };
    }

    // UpdateTicketAssignee is API to update ticket assignee
    rpc UpdateTicketAssignee(UpdateTicketAssigneeRequest) returns (UpdateTicketAssigneeResponse) {
        option (google.api.http) = {
            put: "/api/v1/ticket/{id}/assignee",
            body: "*",
        };
    }

    // CreateFeatureFlag is API to create feature flag
    rpc CreateFeatureFlag(CreateFeatureFlagRequest) returns (CreateFeatureFlagResponse) {
        option (google.api.http) = {
            post: "/api/v1/feature-flag",
            body: "*",
        };
    }

    // UpdateFeatureFlag is API to update feature flag
    rpc UpdateFeatureFlag(UpdateFeatureFlagRequest) returns (UpdateFeatureFlagResponse) {
        option (google.api.http) = {
            put: "/api/v1/feature-flag",
            body: "*",
        };
    }

    // GetFeatureFlag is API to get requested feature flag
    rpc GetFeatureFlag(GetFeatureFlagRequest) returns (GetFeatureFlagResponse) {
        option (google.api.http) = {
            get: "/api/v1/feature-flag",
            body: "*",
        };
    }

    // DeleteFeatureFlag is API to soft delete feature flag
    rpc DeleteFeatureFlag(DeleteFeatureFlagRequest) returns (DeleteFeatureFlagResponse) {
        option (google.api.http) = {
            delete: "/api/v1/feature-flag",
            body: "*",
        };
    }

    // GetFeatureFlagList is API to get feature flag list
    rpc GetFeatureFlagList(GetFeatureFlagListRequest) returns (GetFeatureFlagListResponse) {
        option (google.api.http) = {
            post: "/api/v1/feature-flag/list",
            body: "*",
        };
    }

    // CustomerSearch: API to search customer details based on identifier
    rpc CustomerSearch(CustomerSearchRequest) returns (CustomerSearchResponse) {
        option (google.api.http) = {
            post: "/api/v1/customer/search",
            body: "*"
        };
    }

    // GetCustomers: API to search multiple customer based on identifier
    rpc GetCustomers(GetCustomersRequest) returns (GetCustomersResponse) {
        option (google.api.http) = {
            get: "/api/v1/customers/search",
            body: "*"
        };
    }

    // GetRolesDataSegregation is get roles list API for data segregation purpose
    rpc GetRolesDataSegregation(GetDataSegregationRoleListRequest) returns (GetDataSegregationRoleListResponse) {
        option (google.api.http) = {
            post: "/api/v1/data-segregation/roles",
            body: "*",
        };
    }

    // GetDataSegregation is API for get data segregation detail for specific role
    rpc GetDataSegregation(GetDataSegregationRequest) returns (GetDataSegregationResponse) {
        option (google.api.http) = {
            post: "/api/v1/data-segregation",
            body: "*",
        };
    }

    // UpdateDataSegregation is API for update data segregation
    rpc UpdateDataSegregation(UpdateDataSegregationRequest) returns (UpdateDataSegregationResponse) {
        option (google.api.http) = {
            put: "/api/v1/data-segregation",
            body: "*",
        };
    }

    // GetCustomersDataPoints is API to get specific data points
    rpc GetCustomersDataPoint(CustomerSearchDataPointRequest) returns (CustomerSearchDataPointResponse) {
        option (google.api.http) = {
            post: "/api/v1/customers/search/data-point",
            body: "*"
        };
    }

    // GetCustomerSegements is API to get all customer segemnts
    rpc GetCustomerSegements(google.protobuf.Empty) returns (GetCustomerSegmentsResponse){
        option (google.api.http) = {
            get: "/api/v1/customer-segments",
            body: "*"
        };
    }

    // GetTicketRequestors is API to get all ticket requestors
    rpc GetTicketRequestors(google.protobuf.Empty) returns (GetTicketRequestorsResponse){
        option (google.api.http) = {
            get: "/api/v1/ticket-requestors",
            body: "*"
        };
    }

    // GetCustomerAccounts is API to get specific customer and list of account
    rpc GetCustomerAccounts(GetCustomerAccountRequest) returns (GetCustomerAccountsResponse) {
        option (google.api.http) = {
            post: "/api/v1/customers/search/accounts",
            body: "*"
        };
    }

    // GetTicketFields is API to get ticket fields configuration
    rpc GetTicketFields(GetTicketFieldsRequest) returns (GetTicketFieldsResponse) {
        option (google.api.http) = {
            get: "/api/v1/ticket-fields",
            body: "*"
        };
    }
}

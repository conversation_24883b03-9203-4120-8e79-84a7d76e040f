# for local build, setup environment variables from .env
include .env

.PHONY: ensure-mod lint install-tools test run-local

install-tools:
	@echo ">  Installing tools..."
	(cd /tmp && go install github.com/golangci/golangci-lint/cmd/golangci-lint@v1.59.1)
	#(cd /tmp && go install github.com/golangci/golangci-lint/cmd/golangci-lint)

lint:
	@echo ">  Checking codes..."
	golangci-lint run --out-format code-climate | jq -r '.[] | "\(.location.path):\(.location.lines.begin) \(.description)"'

test:
	@echo ">  Running tests..."
	go test -cover -race -v ./...

test-cover:
	@echo ">  Running tests..."
	go test ./... -v -coverpkg=./... -cverprofile tmp/cover.out.tmp
	cat tmp/cover.out.tmp | grep -vE "/mock_|storage/z_" > tmp/cover.out && rm tmp/cover.out.tmp
	go tool cover -func tmp/cover.out && go tool cover -html=tmp/cover.out -o tmp/cover.html

run-local:
	cp .tools/custom/application.go.txt vendor/gitlab.myteksi.net/dakota/servus/v2/application.go
	cp .tools/custom/aws.consumer.go.txt vendor/gitlab.myteksi.net/dakota/common/aws/sqsClient/consumer/consumer.go
	cp .tools/custom/aws.producer.go.txt vendor/gitlab.myteksi.net/dakota/common/aws/sqsClient/producer/producer.go
	cp .tools/custom/s3client.go.txt vendor/gitlab.myteksi.net/dakota/common/aws/s3client/s3client.go
	# cp .tools/custom/redis.go.txt vendor/gitlab.myteksi.net/dakota/common/redis/redis.go # run only if using single redis node
	cd cmd/onedash-api && go run main.go

ensure-mod:
	go mod download
	go mod vendor

update:
	go-kit update
	go mod vendor

docker-up:
	cd .tools/docker/onedash && docker-compose up -d

docker-down:
	cd .tools/docker/onedash && docker-compose down

docker-migrate:
	cd db/mysql && sqitch deploy --target db:mysql://root:@127.0.0.1:30001/onedash

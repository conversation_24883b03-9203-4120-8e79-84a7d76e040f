package logic

import (
	"context"
	"time"

	"gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

// CalculateDeadline calculates the deadline by adding duration seconds considering working hours and holidays
func (s *CalendarImpl) CalculateDeadline(ctx context.Context, calendarID int64, startTime time.Time, durationSeconds int64) (time.Time, error) {
	// Get calendar information
	calendarInfo, err := s.Repository.GetCalendarInfo(ctx, calendarID)
	if err != nil {
		return time.Time{}, err
	}

	// If no working hours defined, fall back to simple addition
	if len(calendarInfo.WorkingHours) == 0 {
		return startTime.Add(time.Second * time.Duration(durationSeconds)), nil
	}

	workingHoursMap := s.buildWorkingHoursMap(calendarInfo.WorkingHours)
	holidaysMap := s.buildHolidaysMap(calendarInfo.Holidays)

	// Calculate deadline by iterating through time
	return s.calculateDeadlineWithBusinessHours(startTime, durationSeconds, workingHoursMap, holidaysMap), nil
}

// IsBusinessDay checks if the given date is a business day (not a holiday and has working hours)
func (s *CalendarImpl) IsBusinessDay(ctx context.Context, calendarID int64, date time.Time) (bool, error) {
	// Check if it's a holiday
	holidays, err := s.Repository.GetHolidaysByDateRange(ctx, calendarID, date, date)
	if err != nil {
		return false, err
	}

	if len(holidays) > 0 {
		return false, nil
	}

	// Check if there are working hours for this day
	workingHours, err := s.Repository.GetWorkingHoursByCalendarID(ctx, calendarID)
	if err != nil {
		return false, err
	}

	dayOfWeek := int32(date.Weekday())
	for _, wh := range workingHours {
		if wh.DayOfWeek == dayOfWeek {
			return true, nil
		}
	}

	return false, nil
}

// IsWorkingHour checks if the given datetime is within working hours and not a holiday
func (s *CalendarImpl) IsWorkingHour(ctx context.Context, calendarID int64, dateTime time.Time) (bool, error) {
	// Get calendar information
	calendarInfo, err := s.Repository.GetCalendarInfo(ctx, calendarID)
	if err != nil {
		return false, err
	}

	// Build maps for quick lookup
	workingHoursMap := s.buildWorkingHoursMap(calendarInfo.WorkingHours)
	holidaysMap := s.buildHolidaysMap(calendarInfo.Holidays)

	return s.isWorkingTime(dateTime, workingHoursMap, holidaysMap), nil
}

// AddBusinessDays adds the specified number of business days to the start date
func (s *CalendarImpl) AddBusinessDays(ctx context.Context, calendarID int64, startDate time.Time, days int) (time.Time, error) {
	// Get calendar information
	calendarInfo, err := s.Repository.GetCalendarInfo(ctx, calendarID)
	if err != nil {
		return time.Time{}, err
	}

	// Build working hours map
	workingHoursMap := s.buildWorkingHoursMap(calendarInfo.WorkingHours)

	// Build holidays map (get holidays for a reasonable range)
	endRange := startDate.AddDate(0, 0, days*2) // Get holidays for twice the number of days as buffer
	holidays, err := s.Repository.GetHolidaysByDateRange(ctx, calendarID, startDate, endRange)
	if err != nil {
		return time.Time{}, err
	}
	holidaysMap := s.buildHolidaysMap(holidays)

	currentDate := startDate
	businessDaysAdded := 0

	// Iterate through dates until we've added the required business days
	for businessDaysAdded < days {
		currentDate = currentDate.AddDate(0, 0, 1)

		// Check if this is a business day
		dayOfWeek := int32(currentDate.Weekday())
		dateKey := currentDate.Format("2006-01-02")

		// Skip if it's a holiday
		if holidaysMap[dateKey] {
			continue
		}

		// Skip if no working hours for this day
		if _, exists := workingHoursMap[dayOfWeek]; !exists {
			continue
		}

		businessDaysAdded++

		// Safety check to prevent infinite loops
		if currentDate.Sub(startDate) > 365*24*time.Hour {
			break
		}
	}

	return currentDate, nil
}

// buildWorkingHoursMap creates a map of day of week to working hours
func (s *CalendarImpl) buildWorkingHoursMap(workingHours []*storage.CalendarWorkingHoursDTO) map[int32]storage.WorkingHoursInfo {
	workingHoursMap := make(map[int32]storage.WorkingHoursInfo)

	for _, wh := range workingHours {
		workingHoursMap[wh.DayOfWeek] = storage.WorkingHoursInfo{
			DayOfWeek: wh.DayOfWeek,
			StartTime: wh.StartTime,
			EndTime:   wh.EndTime,
		}
	}

	return workingHoursMap
}

// buildHolidaysMap creates a map of dates to holidays for quick lookup
func (s *CalendarImpl) buildHolidaysMap(holidays []*storage.CalendarHolidayDTO) map[string]bool {
	holidaysMap := make(map[string]bool)

	for _, holiday := range holidays {
		dateKey := holiday.HolidayDate.Format("2006-01-02")
		holidaysMap[dateKey] = true
	}

	return holidaysMap
}

// calculateDeadlineWithBusinessHours performs the actual deadline calculation
func (s *CalendarImpl) calculateDeadlineWithBusinessHours(
	startTime time.Time,
	durationSeconds int64,
	workingHoursMap map[int32]storage.WorkingHoursInfo,
	holidaysMap map[string]bool,
) time.Time {
	currentTime := startTime
	remainingSeconds := durationSeconds

	// Iterate through time, counting only business seconds
	for remainingSeconds > 0 {
		// Check if current time is within working hours and not a holiday
		if s.isWorkingTime(currentTime, workingHoursMap, holidaysMap) {
			remainingSeconds--
		}

		// Move to next second
		currentTime = currentTime.Add(time.Second)

		// Safety check to prevent infinite loops (max 1 year)
		if currentTime.Sub(startTime) > 365*24*time.Hour {
			break
		}
	}

	return currentTime
}

// isWorkingTime checks if the given time is within working hours and not a holiday
func (s *CalendarImpl) isWorkingTime(
	t time.Time,
	workingHoursMap map[int32]storage.WorkingHoursInfo,
	holidaysMap map[string]bool,
) bool {
	// Check if it's a holiday
	dateKey := t.Format("2006-01-02")
	if holidaysMap[dateKey] {
		return false
	}

	// Get day of week (0=Sunday, 1=Monday, etc.)
	dayOfWeek := int32(t.Weekday())

	// Check if working hours are defined for this day
	workingHours, exists := workingHoursMap[dayOfWeek]
	if !exists {
		return false
	}

	// Parse working hours times
	startTime, err := time.Parse("15:04:05", workingHours.StartTime)
	if err != nil {
		return false // Invalid time format, consider as non-working time
	}
	endTime, err := time.Parse("15:04:05", workingHours.EndTime)
	if err != nil {
		return false // Invalid time format, consider as non-working time
	}

	// Extract time components for comparison
	currentTimeOfDay := time.Date(0, 1, 1, t.Hour(), t.Minute(), t.Second(), 0, time.UTC)
	startTimeOfDay := time.Date(0, 1, 1, startTime.Hour(), startTime.Minute(), startTime.Second(), 0, time.UTC)
	endTimeOfDay := time.Date(0, 1, 1, endTime.Hour(), endTime.Minute(), endTime.Second(), 0, time.UTC)

	// Check if current time is within working hours
	return (currentTimeOfDay.After(startTimeOfDay) || currentTimeOfDay.Equal(startTimeOfDay)) &&
		(currentTimeOfDay.Before(endTimeOfDay) || currentTimeOfDay.Equal(endTimeOfDay))
}

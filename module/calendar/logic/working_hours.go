package logic

import (
	"context"
	"database/sql"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

// TODO: Can separate later, concept for now

func (s *CalendarImpl) UpsertWorkingHours(ctx context.Context, req *api.UpsertWorkingHoursRequest) error {
	const timeLayout = "15:04:05"

	tx, err := s.Repository.BeginTransaction(ctx)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to begin transaction")
	}
	// Ensure transaction is rolled back on error.
	defer tx.Rollback()

	// Fetch existing data and organize it for efficient processing
	existingHours, err := s.Repository.GetWorkingHoursByCalendarID(ctx, req.Id)
	if err != nil {
		return err
	}

	existingHoursMap := make(map[int32]*calendarStorage.CalendarWorkingHoursDTO)
	for _, wh := range existingHours {
		existingHoursMap[wh.DayOfWeek] = wh
	}

	// Determine what needs to be created, updated, or deleted
	var toCreate []*calendarStorage.CalendarWorkingHoursDTO
	var toUpdate []*calendarStorage.CalendarWorkingHoursDTO

	for _, reqWH := range req.WorkingHours {
		// Validate time format from the request.
		_, err := time.Parse(timeLayout, reqWH.StartTime)
		if err != nil {
			return errorwrapper.Error(apiError.BadRequest, "invalid start_time format. Use HH:MM:SS format")
		}
		_, err = time.Parse(timeLayout, reqWH.EndTime)
		if err != nil {
			return errorwrapper.Error(apiError.BadRequest, "invalid end_time format. Use HH:MM:SS format")
		}

		// Check if a working hour for this day already exists.
		if existing, ok := existingHoursMap[reqWH.DayOfWeek]; ok {
			// It exists: This is an UPDATE.
			existing.StartTime = reqWH.StartTime
			existing.EndTime = reqWH.EndTime
			existing.UpdatedBy = sql.NullInt64{Int64: 1, Valid: true} // Assuming user ID 1
			toUpdate = append(toUpdate, existing)

			// Remove it from the map. Any items left in the map at the end will be deleted.
			delete(existingHoursMap, reqWH.DayOfWeek)
		} else {
			// It does not exist: This is a CREATE.
			newWH := &calendarStorage.CalendarWorkingHoursDTO{
				CalendarID: req.Id,
				DayOfWeek:  reqWH.DayOfWeek,
				StartTime:  reqWH.StartTime,
				EndTime:    reqWH.EndTime,
				CreatedBy:  sql.NullInt64{Int64: 1, Valid: true},
				UpdatedBy:  sql.NullInt64{Int64: 1, Valid: true},
			}
			toCreate = append(toCreate, newWH)
		}
	}

	// Any items remaining in the map are days that were not in the request, so we should delete them.
	var toDeleteIDs []int64
	for _, wh := range existingHoursMap {
		toDeleteIDs = append(toDeleteIDs, wh.ID)
	}

	// Execute batch database operations
	if len(toCreate) > 0 {
		if err := s.Repository.BatchAddWorkingHours(ctx, tx, toCreate); err != nil {
			return err
		}
	}
	if len(toUpdate) > 0 {
		if err := s.Repository.BatchUpdateWorkingHours(ctx, tx, toUpdate); err != nil {
			return err
		}
	}
	if len(toDeleteIDs) > 0 {
		if err := s.Repository.BatchDeleteWorkingHours(ctx, tx, toDeleteIDs); err != nil {
			return err
		}
	}

	return tx.Commit()
}

// BatchAddWorkingHours adds multiple working hours in a transaction
func (s *CalendarImpl) BatchAddWorkingHours(ctx context.Context, tx *sql.Tx, workingHours []*calendarStorage.CalendarWorkingHoursDTO) error {
	return s.Repository.BatchAddWorkingHours(ctx, tx, workingHours)
}

// AddWorkingHours adds working hours for a calendar
func (s *CalendarImpl) AddWorkingHours(ctx context.Context, workingHours *calendarStorage.CalendarWorkingHoursDTO) (int64, error) {

	return s.Repository.AddWorkingHours(ctx, workingHours)
}

// UpdateWorkingHours updates existing working hours
func (s *CalendarImpl) UpdateWorkingHours(ctx context.Context, workingHours *calendarStorage.CalendarWorkingHoursDTO) error {

	return s.Repository.UpdateWorkingHours(ctx, workingHours)
}

// BatchUpdateWorkingHours updates multiple working hours in a transaction
func (s *CalendarImpl) BatchUpdateWorkingHours(ctx context.Context, tx *sql.Tx, workingHours []*calendarStorage.CalendarWorkingHoursDTO) error {
	return s.Repository.BatchUpdateWorkingHours(ctx, tx, workingHours)
}

// DeleteWorkingHours deletes working hours by ID
func (s *CalendarImpl) DeleteWorkingHours(ctx context.Context, id int64) error {
	return s.Repository.DeleteWorkingHours(ctx, id)
}

// BatchDeleteWorkingHours deletes multiple working hours in a transaction
func (s *CalendarImpl) BatchDeleteWorkingHours(ctx context.Context, tx *sql.Tx, ids []int64) error {
	return s.Repository.BatchDeleteWorkingHours(ctx, tx, ids)
}

// GetWorkingHoursByCalendarID retrieves working hours for a calendar
func (s *CalendarImpl) GetWorkingHoursByCalendarID(ctx context.Context, calendarID int64) ([]*calendarStorage.CalendarWorkingHoursDTO, error) {

	return s.Repository.GetWorkingHoursByCalendarID(ctx, calendarID)
}

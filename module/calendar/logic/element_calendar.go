package logic

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

// AssignCalendarToElement assigns a calendar to an element
func (s *CalendarImpl) AssignCalendarToElement(ctx context.Context, req *api.AssignCalendarToElementRequest) (bool, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return false, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Authenticate the request
	user, hasPerm, err := s.Authenticator.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return false, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return false, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this action")
	}

	return true, s.Repository.AssignCalendarToElement(ctx, req.ElementID, req.CalendarID, user.ID)
}

// GetCalendarIDForElement retrieves the calendar ID assigned to an element
func (s *CalendarImpl) GetCalendarIDForElement(ctx context.Context, elementID int64) (int64, error) {

	return s.Repository.GetCalendarIDForElement(ctx, elementID)
}

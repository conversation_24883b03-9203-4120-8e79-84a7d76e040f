package logic

import (
	"context"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// IAuthenticator defines the interface for authentication operations
// This interface allows calendar module to authenticate requests without directly importing permission management logic
//
//go:generate mockery --name=IAuthenticator --output=mock_i_auth --inpackage
type IAuthenticator interface {
	// AuthenticateRequestByElementCode authenticates a request by element code and returns user info and permission status
	AuthenticateRequestByElementCode(ctx context.Context, code constants.ElementCodes, bitwiseValueRequired int64) (*permissionManagementStorage.UserDTO, bool, error)
}

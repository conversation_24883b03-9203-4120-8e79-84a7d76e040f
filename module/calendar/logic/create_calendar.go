package logic

import (
	"context"
	"database/sql"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

// CreateCalendar creates a new calendar
func (s *CalendarImpl) CreateCalendar(ctx context.Context, req *api.CreateCalendarRequest) (int64, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	calendarDTO := &storage.CalendarDTO{
		Name:        req.Name,
		Description: sql.NullString{String: req.Description, Valid: true},
		Type:        req.Type,
		IsActive:    true,
		CreatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:   sql.NullInt64{Int64: 1, Valid: true},
		UpdatedBy:   sql.NullInt64{Int64: 1, Valid: true},
	}

	ID, err := s.Repository.CreateCalendar(ctx, calendarDTO)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.Idem, "failed to create calendar")
	}

	return ID, nil
}

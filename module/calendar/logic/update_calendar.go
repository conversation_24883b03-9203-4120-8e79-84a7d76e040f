package logic

import (
	"context"
	"database/sql"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

// UpdateCalendar updates an existing calendar
func (s *CalendarImpl) UpdateCalendar(ctx context.Context, req *api.UpdateCalendarRequest) (int64, error) {
	// Authenticate the request
	user, hasPerm, err := s.Authenticator.AuthenticateRequestByElementCode(ctx, constants.ModuleConfig, constants.BitwiseValueGeneralUpdate)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return 0, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this action")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	_, err = s.Repository.GetCalendarByID(ctx, req.Id)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.BadRequest, "failed to get calendar")
	}

	calendarDTO := &storage.CalendarDTO{
		ID:          req.Id,
		Name:        req.Name,
		Description: sql.NullString{String: req.Description, Valid: true},
		Type:        req.Type,
		IsActive:    req.IsActive,
		UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy:   sql.NullInt64{Int64: user.ID, Valid: true},
	}

	err = s.Repository.UpdateCalendar(ctx, calendarDTO)
	if err != nil {
		return 0, err
	}

	return req.Id, nil
}

package logic

import (
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

func MakeHolidayAPIStruct(holidays []*calendarStorage.CalendarHolidayDTO) []api.Holiday {
	var res []api.Holiday
	for _, holiday := range holidays {
		auditTrail := api.Holiday{
			Id:          holiday.ID,
			CalendarID:  holiday.CalendarID,
			HolidayDate: holiday.HolidayDate.Format("2006-01-02"),
			Name:        holiday.Name,
			Description: holiday.Description.String,
			CreatedBy:   holiday.CreatedBy.Int64,
			UpdatedBy:   holiday.UpdatedBy.Int64,
		}

		res = append(res, auditTrail)
	}
	return res
}

func MakeWorkingHoursAPIStruct(workingHours []*calendarStorage.CalendarWorkingHoursDTO) []api.WorkingHours {
	var res []api.WorkingHours
	for _, wh := range workingHours {
		auditTrail := api.WorkingHours{
			Id:         wh.ID,
			CalendarID: wh.CalendarID,
			DayOfWeek:  int32(wh.DayOfWeek),
			StartTime:  wh.StartTime,
			EndTime:    wh.EndTime,
			CreatedBy:  wh.CreatedBy.Int64,
			UpdatedBy:  wh.UpdatedBy.Int64,
		}

		res = append(res, auditTrail)
	}
	return res
}

func MakeCalendarAPIStruct(calendar *calendarStorage.CalendarDTO) *api.Calendars {
	return &api.Calendars{
		Id:          calendar.ID,
		Name:        calendar.Name,
		Description: calendar.Description.String,
		Type:        calendar.Type,
		IsActive:    calendar.IsActive,
		CreatedBy:   calendar.CreatedBy.Int64,
		UpdatedBy:   calendar.UpdatedBy.Int64,
	}
}

// ParseTime parses start time from various common formats
func ParseTime(timeStr string) (time.Time, error) {
	// List of supported time formats (in order of preference)
	formats := []string{
		time.RFC3339,           // "2006-01-02T15:04:05Z07:00" (ISO 8601 with timezone)
		time.RFC3339Nano,       // "2006-01-02T15:04:05.999999999Z07:00"
		"2006-01-02T15:04:05Z", // "2006-01-02T15:04:05Z" (UTC)
		"2006-01-02T15:04:05",  // "2006-01-02T15:04:05" (no timezone)
		"2006-01-02 15:04:05",  // "2006-01-02 15:04:05" (space separator)
		"2006-01-02",           // "2006-01-02" (date only, assumes 00:00:00)
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, errorwrapper.Error(apiError.BadRequest, "unsupported time format")
}

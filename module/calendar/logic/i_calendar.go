package logic

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	calendarStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

//go:generate mockery --name=ICalendar --output=mock_i_calendar --inpackage
type ICalendar interface {
	// Calendar Configuration
	CreateCalendar(ctx context.Context, calendar *api.CreateCalendarRequest) (int64, error)
	UpdateCalendar(ctx context.Context, req *api.UpdateCalendarRequest) (int64, error)
	GetCalendarByID(ctx context.Context, id int64) (*calendarStorage.CalendarDTO, error)
	GetCalendarList(ctx context.Context, req *api.GetCalendarListRequest) ([]*calendarStorage.CalendarDTO, error)
	GetCalendarInfoByID(ctx context.Context, id int64) (*calendarStorage.CalendarInfo, error)

	// Working Hours
	AddWorkingHours(ctx context.Context, workingHours *calendarStorage.CalendarWorkingHoursDTO) (int64, error)
	UpdateWorkingHours(ctx context.Context, workingHours *calendarStorage.CalendarWorkingHoursDTO) error
	DeleteWorkingHours(ctx context.Context, id int64) error
	UpsertWorkingHours(ctx context.Context, req *api.UpsertWorkingHoursRequest) error
	GetWorkingHoursByCalendarID(ctx context.Context, calendarID int64) ([]*calendarStorage.CalendarWorkingHoursDTO, error)

	// Holidays
	AddHoliday(ctx context.Context, req *api.AddHolidayRequest) (int64, error)
	AddHolidayRange(ctx context.Context, calendarID int64, startDate, endDate time.Time, name, description string, userID int64) ([]int64, error)
	UpdateHoliday(ctx context.Context, holiday *calendarStorage.CalendarHolidayDTO) error
	UpdateHolidayRange(ctx context.Context, holidayID []int64, name, description string, userID int64) ([]int64, error)
	DeleteHoliday(ctx context.Context, id int64) error
	GetHolidaysByCalendarID(ctx context.Context, calendarID int64, year string) ([]*calendarStorage.CalendarHolidayDTO, error)

	// Business Day Calculations
	CalculateDeadline(ctx context.Context, calendarID int64, startTime time.Time, durationSeconds int64) (time.Time, error)
	IsBusinessDay(ctx context.Context, calendarID int64, date time.Time) (bool, error)
	IsWorkingHour(ctx context.Context, calendarID int64, dateTime time.Time) (bool, error)
	AddBusinessDays(ctx context.Context, calendarID int64, startDate time.Time, days int) (time.Time, error)

	// Element Calendar Assignment
	AssignCalendarToElement(ctx context.Context, req *api.AssignCalendarToElementRequest) (bool, error)
	GetCalendarIDForElement(ctx context.Context, elementID int64) (int64, error)
}

package logic

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	calendarStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

// GetCalendarByID retrieves a calendar by its ID
func (s *CalendarImpl) GetCalendarByID(ctx context.Context, id int64) (*calendarStorage.CalendarDTO, error) {
	return s.Repository.GetCalendarByID(ctx, id)
}

// GetCalendarList retrieves calendars based on query conditions
func (s *CalendarImpl) GetCalendarList(ctx context.Context, req *api.GetCalendarListRequest) ([]*calendarStorage.CalendarDTO, error) {
	// Set default values if not provided
	limit := req.Limit
	if limit <= 0 {
		limit = 50 // Default limit
	}

	offset := req.Offset
	if offset < 0 {
		offset = 0
	}

	conditions := []commonStorage.QueryCondition{
		commonStorage.Limit(int(limit)),
		commonStorage.Offset(int(offset)),
	}

	if req.SearchKey != "" {
		conditions = append(conditions, commonStorage.Like("name", "%"+req.SearchKey+"%", commonStorage.QueryConditionTypeWHERE))
	}

	return s.Repository.GetCalendarList(ctx, conditions)
}

// GetCalendarInfoByID retrieves complete calendar information including working hours and holidays
func (s *CalendarImpl) GetCalendarInfoByID(ctx context.Context, id int64) (*calendarStorage.CalendarInfo, error) {
	return s.Repository.GetCalendarInfo(ctx, id)
}

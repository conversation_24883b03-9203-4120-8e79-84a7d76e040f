package logic

import (
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

type CalendarImpl struct {
	AppConfig     *config.AppConfig           `inject:"config"`
	Repository    storage.ICalendarRepository `inject:"repository.calendar"`
	Authenticator IAuthenticator              `inject:"authenticator"`
}

var (
	// CalendarProcess is the interactor for all calendar logic
	CalendarProcess ICalendar
)

// InitLogic initializes the calendar logic layer
func InitLogic(app *servus.Application, conf *config.AppConfig) {
	storage.InitRepository(app, conf)
	CalendarProcess = NewCalendarProcess(conf)
	app.MustRegister("process.calendar", CalendarProcess)
}

// NewCalendarProcess ...
func NewCalendarProcess(cfg *config.AppConfig) *CalendarImpl {
	return &CalendarImpl{AppConfig: cfg}
}

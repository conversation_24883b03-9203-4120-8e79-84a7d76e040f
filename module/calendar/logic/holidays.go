package logic

import (
	"context"
	"database/sql"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// TODO: Can separate later, concept for now

// AddHoliday adds a holiday for a calendar
func (s *CalendarImpl) AddHoliday(ctx context.Context, req *api.AddHolidayRequest) (int64, error) {
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return 0, status.Errorf(codes.InvalidArgument, "invalid start_date format, please use YYYY-MM-DD: %v", err)
	}

	holidayDTO := &calendarStorage.CalendarHolidayDTO{
		CalendarID:  req.CalendarID,
		HolidayDate: startDate,
		Name:        req.Name,
		Description: sql.NullString{String: req.Description, Valid: req.Description != ""},
		CreatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:   sql.NullInt64{Int64: 1, Valid: true},
		UpdatedBy:   sql.NullInt64{Int64: 1, Valid: true},
	}

	return s.Repository.AddHoliday(ctx, holidayDTO)
}

// AddHolidayRange adds multiple holidays for a date range
func (s *CalendarImpl) AddHolidayRange(ctx context.Context, calendarID int64, startDate, endDate time.Time, name, description string, userID int64) ([]int64, error) {

	return s.Repository.AddHolidayRange(ctx, calendarID, startDate, endDate, name, description, userID)
}

// UpdateHoliday updates an existing holiday
func (s *CalendarImpl) UpdateHoliday(ctx context.Context, holiday *calendarStorage.CalendarHolidayDTO) error {

	return s.Repository.UpdateHoliday(ctx, holiday)
}

// UpdateHolidayRange updates a holiday to span multiple days
func (s *CalendarImpl) UpdateHolidayRange(ctx context.Context, holidayIDs []int64, name, description string, userID int64) ([]int64, error) {
	var updatedHolidays []int64
	for _, id := range holidayIDs {
		ids, err := s.Repository.UpdateHolidayRange(ctx, id, name, description, userID)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to add holiday range")
		}

		updatedHolidays = append(updatedHolidays, ids...)
	}

	return updatedHolidays, nil
}

// DeleteHoliday deletes a holiday by ID
func (s *CalendarImpl) DeleteHoliday(ctx context.Context, id int64) error {
	// TODO: Add authentication and get User details
	return s.Repository.DeleteHoliday(ctx, id)
}

// GetHolidaysByCalendarID retrieves holidays for a calendar
func (s *CalendarImpl) GetHolidaysByCalendarID(ctx context.Context, calendarID int64, year string) ([]*calendarStorage.CalendarHolidayDTO, error) {
	var conditions []commonStorage.QueryCondition
	conditions = append(conditions, commonStorage.EqualTo("calendar_id", calendarID))
	conditions = append(conditions, commonStorage.AscendingOrder("holiday_date"))
	conditions = append(conditions, commonStorage.Limit(100))
	conditions = append(conditions, commonStorage.Offset(0))

	if year != "" {
		conditions = append(conditions, commonStorage.EqualTo("YEAR(holiday_date)", year))
	}

	return s.Repository.GetHolidaysByCalendarID(ctx, conditions)
}

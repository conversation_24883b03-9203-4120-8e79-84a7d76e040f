package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

func (c *CalendarService) UpsertWorkingHours(ctx context.Context, req *api.UpsertWorkingHoursRequest) (*api.UpsertWorkingHoursResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Check if calendar exist
	_, err = calendarLogic.CalendarProcess.GetCalendarByID(ctx, req.CalendarID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to get calendar")
	}

	// handle in upsert working hours logic
	err = calendarLogic.CalendarProcess.UpsertWorkingHours(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to upsert working hours")
	}

	return &api.UpsertWorkingHoursResponse{Success: true}, nil
}

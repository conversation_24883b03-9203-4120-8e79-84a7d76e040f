package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/storage"
)

func (c *CalendarService) GetCalendarList(ctx context.Context, req *api.GetCalendarListRequest) (*api.GetCalendarListResponse, error) {
	calendars, err := calendarLogic.CalendarProcess.GetCalendarList(ctx, req)
	if err != nil {
		return nil, err
	}

	return &api.GetCalendarListResponse{
		Calendars: makeCalendarAPIStruct(calendars),
		Total:     int64(len(calendars)),
	}, nil
}

func makeCalendarAPIStruct(calendars []*storage.CalendarDTO) []api.Calendars {
	var res []api.Calendars
	for _, calendar := range calendars {
		res = append(res, api.Calendars{
			Id:          calendar.ID,
			Name:        calendar.Name,
			Description: calendar.Description.String,
			Type:        calendar.Type,
			IsActive:    calendar.IsActive,
			CreatedBy:   calendar.CreatedBy.Int64,
			UpdatedBy:   calendar.UpdatedBy.Int64,
		})
	}
	return res
}

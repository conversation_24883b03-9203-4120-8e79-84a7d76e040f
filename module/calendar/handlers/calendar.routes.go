// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: calendar.proto
package handlers

import (
	context "context"
	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	v2 "gitlab.myteksi.net/dakota/servus/v2"
)

// RegisterRoutes registers handlers with the Servus library.
func (c *CalendarService) RegisterRoutes(app *v2.Application) {
	app.POST(
		"/api/v1/calendars",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.CreateCalendar(ctx, req.(*api.CreateCalendarRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateCalendarRequest{}),
		v2.WithResponse(&api.CreateCalendarResponse{}),
		v2.WithDescription("Calendar Configuration"),
	)
	app.PUT(
		"/api/v1/calendars/:calendarID",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.UpdateCalendar(ctx, req.(*api.UpdateCalendarRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateCalendarRequest{}),
		v2.WithResponse(&api.UpdateCalendarResponse{}),
	)
	app.GET(
		"/api/v1/calendars/:calendarID",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.GetCalendar(ctx, req.(*api.GetCalendarRequest))
			return res, err
		},
		v2.WithRequest(&api.GetCalendarRequest{}),
		v2.WithResponse(&api.GetCalendarResponse{}),
	)
	app.GET(
		"/api/v1/calendars",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.GetCalendarList(ctx, req.(*api.GetCalendarListRequest))
			return res, err
		},
		v2.WithRequest(&api.GetCalendarListRequest{}),
		v2.WithResponse(&api.GetCalendarListResponse{}),
	)
	app.PUT(
		"/api/v1/calendars/:calendarID/working-hours",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.UpsertWorkingHours(ctx, req.(*api.UpsertWorkingHoursRequest))
			return res, err
		},
		v2.WithRequest(&api.UpsertWorkingHoursRequest{}),
		v2.WithResponse(&api.UpsertWorkingHoursResponse{}),
		v2.WithDescription("Working Hours"),
	)
	app.POST(
		"/api/v1/calendars/:calendarID/holidays",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.AddHoliday(ctx, req.(*api.AddHolidayRequest))
			return res, err
		},
		v2.WithRequest(&api.AddHolidayRequest{}),
		v2.WithResponse(&api.AddHolidayResponse{}),
		v2.WithDescription("Holidays"),
	)
	app.PUT(
		"/api/v1/holidays",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.UpdateHoliday(ctx, req.(*api.UpdateHolidayRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateHolidayRequest{}),
		v2.WithResponse(&api.UpdateHolidayResponse{}),
	)
	app.DELETE(
		"/api/v1/holidays/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.DeleteHoliday(ctx, req.(*api.DeleteHolidayRequest))
			return res, err
		},
		v2.WithRequest(&api.DeleteHolidayRequest{}),
		v2.WithResponse(&api.DeleteHolidayResponse{}),
	)
	app.GET(
		"/api/v1/calendars/:calendarID/holidays",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.GetHolidays(ctx, req.(*api.GetHolidaysRequest))
			return res, err
		},
		v2.WithRequest(&api.GetHolidaysRequest{}),
		v2.WithResponse(&api.GetHolidaysResponse{}),
	)
	app.POST(
		"/api/v1/calendars/calculate-deadline",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.CalculateDeadline(ctx, req.(*api.CalculateDeadlineRequest))
			return res, err
		},
		v2.WithRequest(&api.CalculateDeadlineRequest{}),
		v2.WithResponse(&api.CalculateDeadlineResponse{}),
	)
	app.POST(
		"/api/v1/elements/:elementID/calendar",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := c.AssignCalendarToElement(ctx, req.(*api.AssignCalendarToElementRequest))
			return res, err
		},
		v2.WithRequest(&api.AssignCalendarToElementRequest{}),
		v2.WithResponse(&api.AssignCalendarToElementResponse{}),
		v2.WithDescription("Element Calendar Assignment"),
	)
}

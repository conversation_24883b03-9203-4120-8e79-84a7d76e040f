package handlers

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

// CalculateDeadline handles deadline calculation requests
func (s *CalendarService) CalculateDeadline(ctx context.Context, req *api.CalculateDeadlineRequest) (*api.CalculateDeadlineResponse, error) {
	// Validate calendar ID
	if req.CalendarID <= 0 {
		return nil, errorwrapper.Error(apiError.BadRequest, "calendar_id must be greater than 0")
	}

	// Validate duration
	if req.DurationSeconds <= 0 {
		return nil, errorwrapper.Error(apiError.BadRequest, "duration_seconds must be greater than 0")
	}

	var startTime time.Time
	var err error

	if req.StartTime != "" {
		// ISO 8601 format
		startTime, err = calendarLogic.ParseTime(req.StartTime)
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, "invalid start_time format. Use ISO 8601 format (e.g., '2024-01-15T09:00:00Z' or '2024-01-15T09:00:00+07:00')")
		}
	} else {
		// Use current time as default (most common use case)
		startTime = time.Now()
	}

	// Calculate deadline using business hours
	deadline, err := calendarLogic.CalendarProcess.CalculateDeadline(ctx, req.CalendarID, startTime, req.DurationSeconds)
	if err != nil {
		return nil, err
	}

	// Calculate how many business seconds were actually added
	businessSecondsAdded := calculateBusinessSecondsAdded(startTime, deadline, req.DurationSeconds)

	// Return deadline in ISO 8601 format
	return &api.CalculateDeadlineResponse{
		Deadline:             deadline.Format(time.RFC3339),
		StartTimeUsed:        startTime.Format(time.RFC3339), // Show what start time was used
		BusinessSecondsAdded: businessSecondsAdded,           // Show actual business seconds added
	}, nil
}

// calculateBusinessSecondsAdded calculates how many business seconds were actually added
// This is could useful for debugging and validation
func calculateBusinessSecondsAdded(startTime, deadline time.Time, requestedSeconds int64) int64 {
	// For now, return the requested seconds
	// In a more sophisticated implementation, you could calculate the actual business seconds
	// by iterating through the time range and counting only business seconds
	actualDuration := deadline.Sub(startTime)

	// If the actual duration is less than or equal to requested duration,
	// it means all seconds were business seconds
	if actualDuration.Seconds() <= float64(requestedSeconds) {
		return int64(actualDuration.Seconds())
	}

	// Otherwise, return the requested amount (the business seconds that were added)
	return requestedSeconds
}

package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

func (c *CalendarService) UpdateCalendar(ctx context.Context, req *api.UpdateCalendarRequest) (*api.UpdateCalendarResponse, error) {
	ID, err := calendarLogic.CalendarProcess.UpdateCalendar(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to update calendar")
	}

	return &api.UpdateCalendarResponse{Id: ID}, nil
}

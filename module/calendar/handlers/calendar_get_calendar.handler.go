package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

func (c *CalendarService) GetCalendar(ctx context.Context, req *api.GetCalendarRequest) (*api.GetCalendarResponse, error) {
	calendarInfo, err := calendarLogic.CalendarProcess.GetCalendarInfoByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &api.GetCalendarResponse{
		Calendar:     calendarLogic.MakeCalendarAPIStruct(calendarInfo.Calendar),
		WorkingHours: calendarLogic.MakeWorkingHoursAPIStruct(calendarInfo.WorkingHours),
		Holidays:     calendarLogic.MakeHolidayAPIStruct(calendarInfo.Holidays),
	}, nil
}

package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

func (c *CalendarService) UpdateHoliday(ctx context.Context, req *api.UpdateHolidayRequest) (*api.UpdateHolidayResponse, error) {
	ids, err := calendarLogic.CalendarProcess.UpdateHolidayRange(ctx, req.Ids, req.Name, req.Description, 1)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to add holiday range")
	}

	return &api.UpdateHolidayResponse{
		Ids:         ids,
		DaysUpdated: int32(len(ids)),
	}, nil
}

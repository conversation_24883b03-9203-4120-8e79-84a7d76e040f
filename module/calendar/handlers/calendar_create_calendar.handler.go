package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

// CreateCalendar Add Calendar
func (c *CalendarService) CreateCalendar(ctx context.Context, req *api.CreateCalendarRequest) (*api.CreateCalendarResponse, error) {
	ID, err := calendarLogic.CalendarProcess.CreateCalendar(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to create calendar")
	}

	return &api.CreateCalendarResponse{
		Id: ID,
	}, nil
}

package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

func (c *CalendarService) GetHolidays(ctx context.Context, req *api.GetHolidaysRequest) (*api.GetHolidaysResponse, error) {
	holidays, err := calendarLogic.CalendarProcess.GetHolidaysByCalendarID(ctx, req.CalendarID, req.Year)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get holidays")
	}

	return &api.GetHolidaysResponse{
		Holidays: calendarLogic.MakeHolidayAPIStruct(holidays),
	}, nil

}

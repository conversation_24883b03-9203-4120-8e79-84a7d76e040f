package handlers

import (
	context "context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

// Element Calendar Assignment
func (c *CalendarService) AssignCalendarToElement(ctx context.Context, req *api.AssignCalendarToElementRequest) (*api.AssignCalendarToElementResponse, error) {
	isSuccess, err := calendarLogic.CalendarProcess.AssignCalendarToElement(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to assign calendar to element")
	}

	return &api.AssignCalendarToElementResponse{Success: isSuccess}, nil
}

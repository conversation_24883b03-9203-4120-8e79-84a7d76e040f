package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

func (c *CalendarService) DeleteHoliday(ctx context.Context, req *api.DeleteHolidayRequest) (*api.DeleteHolidayResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	err = calendarLogic.CalendarProcess.DeleteHoliday(ctx, req.Id)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to delete holiday")
	}

	return &api.DeleteHolidayResponse{Success: true}, nil
}

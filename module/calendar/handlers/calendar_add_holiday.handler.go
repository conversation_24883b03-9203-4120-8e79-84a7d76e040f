package handlers

import (
	context "context"
	"time"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (c *CalendarService) AddHoliday(ctx context.Context, req *api.AddHolidayRequest) (*api.AddHolidayResponse, error) {
	// TODO: Add Permission Control
	const dateLayout = "2006-01-02"

	startDate, err := time.Parse(dateLayout, req.StartDate)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid start_date format, please use YYYY-MM-DD: %v", err)
	}

	var createdHolidays []api.AddHolidayResponse_CreatedHoliday

	// if req got end date add range
	if req.EndDate != "" {
		var endDate time.Time
		endDate, err = time.Parse(dateLayout, req.EndDate)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "invalid end_date format, please use YYYY-MM-DD: %v", err)
		}

		holidays, err := calendarLogic.CalendarProcess.AddHolidayRange(ctx, req.Id, startDate, endDate, req.Name, req.Description, 1)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to add holiday range")
		}

		currentDate := startDate
		for _, id := range holidays {
			createdHolidays = append(createdHolidays, api.AddHolidayResponse_CreatedHoliday{
				Id:   id,
				Date: currentDate.Format(dateLayout),
			})
			currentDate = currentDate.AddDate(0, 0, 1)
		}
	} else {
		holiday, err := calendarLogic.CalendarProcess.AddHoliday(ctx, req)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to add holiday")
		}

		createdHolidays = append(createdHolidays, api.AddHolidayResponse_CreatedHoliday{
			Id:   holiday,
			Date: startDate.Format(dateLayout),
		})
	}

	return &api.AddHolidayResponse{
		CreatedHolidays: createdHolidays,
	}, nil
}

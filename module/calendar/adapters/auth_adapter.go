package adapters

import (
	"context"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
)

// AuthAdapter implements the IAuthenticator interface by delegating to permission management logic
type AuthAdapter struct{}

// NewAuthAdapter creates a new AuthAdapter instance
func NewAuthAdapter() calendarLogic.IAuthenticator {
	return &AuthAdapter{}
}

// AuthenticateRequestByElementCode implements the IAuthenticator interface
func (a *AuthAdapter) AuthenticateRequestByElementCode(ctx context.Context, code constants.ElementCodes, bitwiseValueRequired int64) (*permissionManagementStorage.UserDTO, bool, error) {
	return permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, code, bitwiseValueRequired)
}

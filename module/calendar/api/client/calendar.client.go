// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: calendar.proto
package client

import (
	bytes "bytes"
	context "context"
	fmt "fmt"
	_go "github.com/json-iterator/go"
	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	http "net/http"
)

// CalendarClient makes calls to Calendar service.
type CalendarClient struct {
	machinery klient.RoundTripper
}

// MakeCalendarClient instantiates a new CalendarClient.
// Deprecated: Use NewCalendarClient instead
func MakeCalendarClient(initializer klient.Initializer) (*CalendarClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &CalendarClient{
		machinery: roundTripper,
	}, nil
}

// NewCalendarClient instantiates a new CalendarClient.
func NewCalendarClient(baseURL string, options ...klient.Option) (*CalendarClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &CalendarClient{
		machinery: roundTripper,
	}, nil
}

// Calendar Configuration
func (c *CalendarClient) CreateCalendar(ctx context.Context, req *api.CreateCalendarRequest) (*api.CreateCalendarResponse, error) {
	reqShell := (*CreateCalendarRequestShell)(req)
	resShell := &CreateCalendarResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createCalendarDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateCalendarResponse)(resShell), err
}

func (c *CalendarClient) UpdateCalendar(ctx context.Context, req *api.UpdateCalendarRequest) (*api.UpdateCalendarResponse, error) {
	reqShell := (*UpdateCalendarRequestShell)(req)
	resShell := &UpdateCalendarResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateCalendarDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateCalendarResponse)(resShell), err
}

func (c *CalendarClient) GetCalendar(ctx context.Context, req *api.GetCalendarRequest) (*api.GetCalendarResponse, error) {
	reqShell := (*GetCalendarRequestShell)(req)
	resShell := &GetCalendarResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCalendarDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCalendarResponse)(resShell), err
}

func (c *CalendarClient) GetCalendarList(ctx context.Context, req *api.GetCalendarListRequest) (*api.GetCalendarListResponse, error) {
	reqShell := (*GetCalendarListRequestShell)(req)
	resShell := &GetCalendarListResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getCalendarListDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetCalendarListResponse)(resShell), err
}

// Working Hours
func (c *CalendarClient) UpsertWorkingHours(ctx context.Context, req *api.UpsertWorkingHoursRequest) (*api.UpsertWorkingHoursResponse, error) {
	reqShell := (*UpsertWorkingHoursRequestShell)(req)
	resShell := &UpsertWorkingHoursResponseShell{}
	clientCtx := klient.MakeContext(ctx, &upsertWorkingHoursDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpsertWorkingHoursResponse)(resShell), err
}

// Holidays
func (c *CalendarClient) AddHoliday(ctx context.Context, req *api.AddHolidayRequest) (*api.AddHolidayResponse, error) {
	reqShell := (*AddHolidayRequestShell)(req)
	resShell := &AddHolidayResponseShell{}
	clientCtx := klient.MakeContext(ctx, &addHolidayDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AddHolidayResponse)(resShell), err
}

func (c *CalendarClient) UpdateHoliday(ctx context.Context, req *api.UpdateHolidayRequest) (*api.UpdateHolidayResponse, error) {
	reqShell := (*UpdateHolidayRequestShell)(req)
	resShell := &UpdateHolidayResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateHolidayDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateHolidayResponse)(resShell), err
}

func (c *CalendarClient) DeleteHoliday(ctx context.Context, req *api.DeleteHolidayRequest) (*api.DeleteHolidayResponse, error) {
	reqShell := (*DeleteHolidayRequestShell)(req)
	resShell := &DeleteHolidayResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deleteHolidayDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.DeleteHolidayResponse)(resShell), err
}

func (c *CalendarClient) GetHolidays(ctx context.Context, req *api.GetHolidaysRequest) (*api.GetHolidaysResponse, error) {
	reqShell := (*GetHolidaysRequestShell)(req)
	resShell := &GetHolidaysResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getHolidaysDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetHolidaysResponse)(resShell), err
}

func (c *CalendarClient) CalculateDeadline(ctx context.Context, req *api.CalculateDeadlineRequest) (*api.CalculateDeadlineResponse, error) {
	reqShell := (*CalculateDeadlineRequestShell)(req)
	resShell := &CalculateDeadlineResponseShell{}
	clientCtx := klient.MakeContext(ctx, &calculateDeadlineDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CalculateDeadlineResponse)(resShell), err
}

// Element Calendar Assignment
func (c *CalendarClient) AssignCalendarToElement(ctx context.Context, req *api.AssignCalendarToElementRequest) (*api.AssignCalendarToElementResponse, error) {
	reqShell := (*AssignCalendarToElementRequestShell)(req)
	resShell := &AssignCalendarToElementResponseShell{}
	clientCtx := klient.MakeContext(ctx, &assignCalendarToElementDescriptor)
	err := c.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AssignCalendarToElementResponse)(resShell), err
}

// CreateCalendarRequestShell is a wrapper to make the object a klient.Request
type CreateCalendarRequestShell api.CreateCalendarRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateCalendarRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateCalendarResponseShell is a wrapper to make the object a klient.Request
type CreateCalendarResponseShell api.CreateCalendarResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateCalendarResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateCalendarRequestShell is a wrapper to make the object a klient.Request
type UpdateCalendarRequestShell api.UpdateCalendarRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateCalendarRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars/" + fmt.Sprint(u.Id)
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = 0

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateCalendarResponseShell is a wrapper to make the object a klient.Request
type UpdateCalendarResponseShell api.UpdateCalendarResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateCalendarResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetCalendarRequestShell is a wrapper to make the object a klient.Request
type GetCalendarRequestShell api.GetCalendarRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCalendarRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetCalendarResponseShell is a wrapper to make the object a klient.Request
type GetCalendarResponseShell api.GetCalendarResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCalendarResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetCalendarListRequestShell is a wrapper to make the object a klient.Request
type GetCalendarListRequestShell api.GetCalendarListRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetCalendarListRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetCalendarListResponseShell is a wrapper to make the object a klient.Request
type GetCalendarListResponseShell api.GetCalendarListResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetCalendarListResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpsertWorkingHoursRequestShell is a wrapper to make the object a klient.Request
type UpsertWorkingHoursRequestShell api.UpsertWorkingHoursRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpsertWorkingHoursRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars/" + fmt.Sprint(u.CalendarID) + "/working-hours"
	fullURL := baseURL + filledPath

	pathVar0 := u.CalendarID
	u.CalendarID = 0

	defer func() {
		u.CalendarID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpsertWorkingHoursResponseShell is a wrapper to make the object a klient.Request
type UpsertWorkingHoursResponseShell api.UpsertWorkingHoursResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpsertWorkingHoursResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// AddHolidayRequestShell is a wrapper to make the object a klient.Request
type AddHolidayRequestShell api.AddHolidayRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (a *AddHolidayRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars/" + fmt.Sprint(a.CalendarID) + "/holidays"
	fullURL := baseURL + filledPath

	pathVar0 := a.CalendarID
	a.CalendarID = 0

	defer func() {
		a.CalendarID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(a)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// AddHolidayResponseShell is a wrapper to make the object a klient.Request
type AddHolidayResponseShell api.AddHolidayResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (a *AddHolidayResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(a)
}

// UpdateHolidayRequestShell is a wrapper to make the object a klient.Request
type UpdateHolidayRequestShell api.UpdateHolidayRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateHolidayRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/holidays"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateHolidayResponseShell is a wrapper to make the object a klient.Request
type UpdateHolidayResponseShell api.UpdateHolidayResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateHolidayResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// DeleteHolidayRequestShell is a wrapper to make the object a klient.Request
type DeleteHolidayRequestShell api.DeleteHolidayRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (d *DeleteHolidayRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/holidays/" + fmt.Sprint(d.Id)
	fullURL := baseURL + filledPath

	return http.NewRequest("DELETE", fullURL, nil)
}

// DeleteHolidayResponseShell is a wrapper to make the object a klient.Request
type DeleteHolidayResponseShell api.DeleteHolidayResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (d *DeleteHolidayResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(d)
}

// GetHolidaysRequestShell is a wrapper to make the object a klient.Request
type GetHolidaysRequestShell api.GetHolidaysRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetHolidaysRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars/" + fmt.Sprint(g.CalendarID) + "/holidays"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetHolidaysResponseShell is a wrapper to make the object a klient.Request
type GetHolidaysResponseShell api.GetHolidaysResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetHolidaysResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CalculateDeadlineRequestShell is a wrapper to make the object a klient.Request
type CalculateDeadlineRequestShell api.CalculateDeadlineRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CalculateDeadlineRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/calendars/calculate-deadline"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CalculateDeadlineResponseShell is a wrapper to make the object a klient.Request
type CalculateDeadlineResponseShell api.CalculateDeadlineResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CalculateDeadlineResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// AssignCalendarToElementRequestShell is a wrapper to make the object a klient.Request
type AssignCalendarToElementRequestShell api.AssignCalendarToElementRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (a *AssignCalendarToElementRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/elements/" + fmt.Sprint(a.ElementID) + "/calendar"
	fullURL := baseURL + filledPath

	pathVar0 := a.ElementID
	a.ElementID = 0

	defer func() {
		a.ElementID = pathVar0
	}()

	jsonBytes, err := _go.Marshal(a)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// AssignCalendarToElementResponseShell is a wrapper to make the object a klient.Request
type AssignCalendarToElementResponseShell api.AssignCalendarToElementResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (a *AssignCalendarToElementResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(a)
}

var createCalendarDescriptor = klient.EndpointDescriptor{
	Name:        "CreateCalendar",
	Description: "Calendar Configuration",
	Method:      "POST",
	Path:        "/api/v1/calendars",
}

var updateCalendarDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateCalendar",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/calendars/{id}",
}

var getCalendarDescriptor = klient.EndpointDescriptor{
	Name:        "GetCalendar",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/calendars/{id}",
}

var getCalendarListDescriptor = klient.EndpointDescriptor{
	Name:        "GetCalendarList",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/calendars",
}

var upsertWorkingHoursDescriptor = klient.EndpointDescriptor{
	Name:        "UpsertWorkingHours",
	Description: "Working Hours",
	Method:      "PUT",
	Path:        "/api/v1/calendars/{calendarID}/working-hours",
}

var addHolidayDescriptor = klient.EndpointDescriptor{
	Name:        "AddHoliday",
	Description: "Holidays",
	Method:      "POST",
	Path:        "/api/v1/calendars/{calendarID}/holidays",
}

var updateHolidayDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateHoliday",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/holidays",
}

var deleteHolidayDescriptor = klient.EndpointDescriptor{
	Name:        "DeleteHoliday",
	Description: "",
	Method:      "DELETE",
	Path:        "/api/v1/holidays/{id}",
}

var getHolidaysDescriptor = klient.EndpointDescriptor{
	Name:        "GetHolidays",
	Description: "",
	Method:      "GET",
	Path:        "/api/v1/calendars/{calendarID}/holidays",
}

var calculateDeadlineDescriptor = klient.EndpointDescriptor{
	Name:        "CalculateDeadline",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/calendars/calculate-deadline",
}

var assignCalendarToElementDescriptor = klient.EndpointDescriptor{
	Name:        "AssignCalendarToElement",
	Description: "Element Calendar Assignment",
	Method:      "POST",
	Path:        "/api/v1/elements/{elementID}/calendar",
}

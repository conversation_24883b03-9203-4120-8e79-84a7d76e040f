// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: calendar.proto
package api

import (
	context "context"
)

type Calendars struct {
	Id          int64  `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Type        string `json:"type,omitempty"`
	IsActive    bool   `json:"isActive,omitempty"`
	CreatedBy   int64  `json:"createdBy,omitempty"`
	UpdatedBy   int64  `json:"updatedBy,omitempty"`
}

type WorkingHours struct {
	Id         int64  `json:"id,omitempty"`
	CalendarID int64  `json:"calendarID,omitempty"`
	DayOfWeek  int32  `json:"dayOfWeek,omitempty"`
	StartTime  string `json:"startTime,omitempty"`
	EndTime    string `json:"endTime,omitempty"`
	CreatedBy  int64  `json:"createdBy,omitempty"`
	UpdatedBy  int64  `json:"updatedBy,omitempty"`
}

type Holiday struct {
	Id          int64  `json:"id,omitempty"`
	CalendarID  int64  `json:"calendarID,omitempty"`
	HolidayDate string `json:"holidayDate,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	CreatedBy   int64  `json:"createdBy,omitempty"`
	UpdatedBy   int64  `json:"updatedBy,omitempty"`
}

type CreateCalendarRequest struct {
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Type        string `json:"type,omitempty"`
	IsActive    bool   `json:"isActive,omitempty"`
}

type CreateCalendarResponse struct {
	Id int64 `json:"id,omitempty"`
}

type UpdateCalendarRequest struct {
	Id          int64  `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Type        string `json:"type,omitempty"`
	IsActive    bool   `json:"isActive,omitempty"`
}

type UpdateCalendarResponse struct {
	Id int64 `json:"id,omitempty"`
}

type GetCalendarRequest struct {
	Id int64 `json:"id,omitempty"`
}

type GetCalendarResponse struct {
	Calendar     *Calendars     `json:"calendar,omitempty"`
	WorkingHours []WorkingHours `json:"workingHours,omitempty"`
	Holidays     []Holiday      `json:"holidays,omitempty"`
}

type GetCalendarListRequest struct {
	SearchKey string `json:"searchKey,omitempty"`
	Offset    int64  `json:"offset,omitempty"`
	Limit     int64  `json:"limit,omitempty"`
}

type GetCalendarListResponse struct {
	Calendars []Calendars `json:"calendars,omitempty"`
	Total     int64       `json:"total,omitempty"`
}

type UpsertWorkingHoursRequest struct {
	CalendarID   int64          `json:"calendarID,omitempty"`
	WorkingHours []WorkingHours `json:"workingHours,omitempty"`
}

type UpsertWorkingHoursResponse struct {
	Success bool `json:"success,omitempty"`
}

type AddHolidayRequest struct {
	CalendarID  int64  `json:"calendarID,omitempty"`
	StartDate   string `json:"startDate,omitempty"`
	EndDate     string `json:"endDate,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
}

type AddHolidayResponse struct {
	CreatedHolidays []AddHolidayResponse_CreatedHoliday `json:"createdHolidays,omitempty"`
}

type AddHolidayResponse_CreatedHoliday struct {
	Id   int64  `json:"id,omitempty"`
	Date string `json:"date,omitempty"`
}

type UpdateHolidayRequest struct {
	Ids         []int64 `json:"ids,omitempty"`
	Name        string  `json:"name,omitempty"`
	Description string  `json:"description,omitempty"`
}

type UpdateHolidayResponse struct {
	Ids         []int64 `json:"ids,omitempty"`
	DaysUpdated int32   `json:"daysUpdated,omitempty"`
}

type DeleteHolidayRequest struct {
	Id int64 `json:"id,omitempty"`
}

type DeleteHolidayResponse struct {
	Success bool `json:"success,omitempty"`
}

type GetHolidaysRequest struct {
	CalendarID int64  `json:"calendarID,omitempty"`
	Year       string `json:"year,omitempty"`
}

type GetHolidaysResponse struct {
	Holidays []Holiday `json:"holidays,omitempty"`
}

// Business Day Calculation
type CalculateDeadlineRequest struct {
	CalendarID      int64  `json:"calendarID,omitempty"`
	StartTime       string `json:"startTime,omitempty"`
	DurationSeconds int64  `json:"durationSeconds,omitempty"`
}

type CalculateDeadlineResponse struct {
	Deadline             string `json:"deadline,omitempty"`
	StartTimeUsed        string `json:"startTimeUsed,omitempty"`
	BusinessSecondsAdded int64  `json:"businessSecondsAdded,omitempty"`
}

type AssignCalendarToElementRequest struct {
	ElementID  int64 `json:"elementID,omitempty"`
	CalendarID int64 `json:"calendarID,omitempty"`
}

type AssignCalendarToElementResponse struct {
	Success bool `json:"success,omitempty"`
}

// Calendar
type Calendar interface {
	// Calendar Configuration
	CreateCalendar(ctx context.Context, req *CreateCalendarRequest) (*CreateCalendarResponse, error)
	UpdateCalendar(ctx context.Context, req *UpdateCalendarRequest) (*UpdateCalendarResponse, error)
	GetCalendar(ctx context.Context, req *GetCalendarRequest) (*GetCalendarResponse, error)
	GetCalendarList(ctx context.Context, req *GetCalendarListRequest) (*GetCalendarListResponse, error)
	// Working Hours
	UpsertWorkingHours(ctx context.Context, req *UpsertWorkingHoursRequest) (*UpsertWorkingHoursResponse, error)
	// Holidays
	AddHoliday(ctx context.Context, req *AddHolidayRequest) (*AddHolidayResponse, error)
	UpdateHoliday(ctx context.Context, req *UpdateHolidayRequest) (*UpdateHolidayResponse, error)
	DeleteHoliday(ctx context.Context, req *DeleteHolidayRequest) (*DeleteHolidayResponse, error)
	GetHolidays(ctx context.Context, req *GetHolidaysRequest) (*GetHolidaysResponse, error)
	CalculateDeadline(ctx context.Context, req *CalculateDeadlineRequest) (*CalculateDeadlineResponse, error)
	// Element Calendar Assignment
	AssignCalendarToElement(ctx context.Context, req *AssignCalendarToElementRequest) (*AssignCalendarToElementResponse, error)
}

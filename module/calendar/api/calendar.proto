syntax = "proto3";

package calendar;

option go_package = "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/calendar/api";

import "google/api/annotations.proto";

message Calendars {
    int64 id = 1;
    string name = 2;
    string description = 3;
    string type = 4;
    bool isActive = 5;
    int64 createdBy = 8;
    int64 updatedBy = 9;
}

message WorkingHours {
    int64 id = 1;
    int64 calendarID = 2;
    int32 dayOfWeek = 3;
    string startTime = 4;
    string endTime = 5;
    int64 createdBy = 8;
    int64 updatedBy = 9;
}

message Holiday {
    int64 id = 1;
    int64 calendarID = 2;
    string holidayDate = 3;
    string name = 4;
    string description = 5;
    int64 createdBy = 8;
    int64 updatedBy = 9;
}

message CreateCalendarRequest {
    string name = 1;
    string description = 2;
    string type = 3;
    bool isActive = 4;
}

message CreateCalendarResponse {
    int64 id = 1;
}

message UpdateCalendarRequest {
    int64 id = 1;
    string name = 2;
    string description = 3;
    string type = 4;
    bool isActive = 5;
}

message UpdateCalendarResponse {
    int64 id = 1;
}

message GetCalendarRequest {
    int64 id = 1;
}

message GetCalendarResponse {
    Calendars calendar = 1;
    repeated WorkingHours workingHours = 2;
    repeated Holiday holidays = 3;
}

message GetCalendarListRequest {
    string searchKey = 1;
    int64 offset = 2;
    int64 limit = 3;
}

message GetCalendarListResponse {
    repeated Calendars calendars = 1;
    int64 total = 2;
}

message UpsertWorkingHoursRequest {
    int64 calendarID = 1;
    repeated WorkingHours workingHours = 2;
}

message UpsertWorkingHoursResponse {
    bool success = 1;
}

message AddHolidayRequest {
    int64 calendarID = 1;
    string startDate = 2;
    string endDate = 3;
    string name = 4;
    string description = 5;
}

message AddHolidayResponse {
    message CreatedHoliday {
        int64 id = 1;
        string date = 2;
    }
    repeated CreatedHoliday createdHolidays = 1;
}

message UpdateHolidayRequest {
    repeated int64 ids = 1;
    string name = 2;
    string description = 3;
}

message UpdateHolidayResponse {
    repeated int64 ids = 1;
    int32 days_updated = 2;
}

message DeleteHolidayRequest {
    int64 id = 1;
}

message DeleteHolidayResponse {
    bool success = 1;
}

message GetHolidaysRequest {
    int64 calendarID = 1;
    string year = 2;
}

message GetHolidaysResponse {
    repeated Holiday holidays = 1;
}

// Business Day Calculation
message CalculateDeadlineRequest {
    int64 calendarID = 1;
    string start_time = 2;
    int64 duration_seconds = 3;
}

message CalculateDeadlineResponse {
    string deadline = 1;
    string startTimeUsed = 2;
    int64 businessSecondsAdded = 3;
}

message AssignCalendarToElementRequest {
  int64 elementID = 1;
  int64 calendarID = 2;
}

message AssignCalendarToElementResponse {
  bool success = 1;
}

// Calendar
service Calendar {
    // Calendar Configuration
    rpc CreateCalendar(CreateCalendarRequest) returns (CreateCalendarResponse) {
        option (google.api.http) = {
            post: "/api/v1/calendars",
            body: "*",
        };
    };
    rpc UpdateCalendar(UpdateCalendarRequest) returns (UpdateCalendarResponse) {
        option (google.api.http) = {
            put: "/api/v1/calendars/{calendarID}",
            body: "*",
        };
    };
    rpc GetCalendar(GetCalendarRequest) returns (GetCalendarResponse) {
        option (google.api.http) = {
            get: "/api/v1/calendars/{calendarID}",
        };
    };
    rpc GetCalendarList(GetCalendarListRequest) returns (GetCalendarListResponse) {
        option (google.api.http) = {
            get: "/api/v1/calendars",
        };
    };

    // Working Hours
    rpc UpsertWorkingHours(UpsertWorkingHoursRequest) returns (UpsertWorkingHoursResponse) {
        option (google.api.http) = {
            put: "/api/v1/calendars/{calendarID}/working-hours",
            body: "*",
        };
    };

    // Holidays
    rpc AddHoliday(AddHolidayRequest) returns (AddHolidayResponse) {
        option (google.api.http) = {
            post: "/api/v1/calendars/{calendarID}/holidays"
            body: "*"
        };
    }

    rpc UpdateHoliday(UpdateHolidayRequest) returns (UpdateHolidayResponse) {
        option (google.api.http) = {
            put: "/api/v1/holidays" // Using PUT on the collection for a bulk update
            body: "*"
        };
    }

    rpc DeleteHoliday(DeleteHolidayRequest) returns (DeleteHolidayResponse) {
        option (google.api.http) = {
            delete: "/api/v1/holidays/{id}",
        };
    };
    rpc GetHolidays(GetHolidaysRequest) returns (GetHolidaysResponse) {
        option (google.api.http) = {
            get: "/api/v1/calendars/{calendarID}/holidays",
        };
    };

    rpc CalculateDeadline(CalculateDeadlineRequest) returns (CalculateDeadlineResponse) {
        option (google.api.http) = {
            post: "/api/v1/calendars/calculate-deadline",
            body: "*",
        };
    };

    // Element Calendar Assignment
    rpc AssignCalendarToElement(AssignCalendarToElementRequest) returns (AssignCalendarToElementResponse) {
        option (google.api.http) = {
            post: "/api/v1/elements/{elementID}/calendar",
            body: "*",
        };
    };
}

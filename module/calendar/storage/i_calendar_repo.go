package storage

import (
	"context"
	"database/sql"
	"time"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// ICalendarRepository defines the interface for calendar data access
type ICalendarRepository interface {
	// Calendar operations
	CreateCalendar(ctx context.Context, calendar *CalendarDTO) (int64, error)
	UpdateCalendar(ctx context.Context, calendar *CalendarDTO) error
	GetCalendarByID(ctx context.Context, id int64) (*CalendarDTO, error)
	GetCalendarList(ctx context.Context, conditions []commonStorage.QueryCondition) ([]*CalendarDTO, error)

	// Working hours operations
	AddWorkingHours(ctx context.Context, workingHours *CalendarWorkingHoursDTO) (int64, error)
	BatchAddWorkingHours(ctx context.Context, tx *sql.Tx, workingHours []*CalendarWorkingHoursDTO) error
	UpdateWorkingHours(ctx context.Context, workingHours *CalendarWorkingHoursDTO) error
	BatchUpdateWorkingHours(ctx context.Context, tx *sql.Tx, workingHours []*CalendarWorkingHoursDTO) error
	DeleteWorkingHours(ctx context.Context, id int64) error
	BatchDeleteWorkingHours(ctx context.Context, tx *sql.Tx, ids []int64) error
	GetWorkingHoursByCalendarID(ctx context.Context, calendarID int64) ([]*CalendarWorkingHoursDTO, error)

	// Holiday operations
	GetHolidayByID(ctx context.Context, id int64) (*CalendarHolidayDTO, error)
	AddHoliday(ctx context.Context, holiday *CalendarHolidayDTO) (int64, error)
	AddHolidayRange(ctx context.Context, calendarID int64, startDate, endDate time.Time, name, description string, userID int64) ([]int64, error)
	UpdateHoliday(ctx context.Context, holiday *CalendarHolidayDTO) error
	UpdateHolidayRange(ctx context.Context, holidayID int64, name, description string, userID int64) ([]int64, error)
	DeleteHoliday(ctx context.Context, id int64) error
	GetHolidaysByCalendarID(ctx context.Context, conditions []commonStorage.QueryCondition) ([]*CalendarHolidayDTO, error)
	GetHolidaysByDateRange(ctx context.Context, calendarID int64, startDate, endDate time.Time) ([]*CalendarHolidayDTO, error)

	// Complete calendar information
	GetCalendarInfo(ctx context.Context, calendarID int64) (*CalendarInfo, error)

	// Element calendar mapping operations
	GetCalendarIDForElement(ctx context.Context, elementID int64) (int64, error)
	AssignCalendarToElement(ctx context.Context, elementID, calendarID int64, userID int64) error

	// General repository operations
	BeginTransaction(ctx context.Context) (*sql.Tx, error)
	CommitTransaction(ctx context.Context, tx *sql.Tx) error
}

// CalendarRepositoryImpl implements ICalendarRepository
type CalendarRepositoryImpl struct {
	AppConfig *config.AppConfig `inject:"config"`
}

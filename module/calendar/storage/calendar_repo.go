package storage

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateCalendar creates a new calendar record
func (r *CalendarRepositoryImpl) CreateCalendar(ctx context.Context, calendar *CalendarDTO) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		INSERT INTO calendars (name, description, type, is_active, created_at, updated_at, created_by, updated_by)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := db.ExecContext(ctx, query,
		calendar.Name, calendar.Description, calendar.Type, calendar.IsActive,
		calendar.CreatedAt, calendar.UpdatedAt, calendar.CreatedBy, calendar.UpdatedBy)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// UpdateCalendar updates an existing calendar record
func (r *CalendarRepositoryImpl) UpdateCalendar(ctx context.Context, calendar *CalendarDTO) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		UPDATE calendars 
		SET name = ?, description = ?, type = ?, is_active = ?, updated_at = ?, updated_by = ?
		WHERE id = ?
	`

	_, err = db.ExecContext(ctx, query,
		calendar.Name, calendar.Description, calendar.Type, calendar.IsActive,
		calendar.UpdatedAt, calendar.UpdatedBy, calendar.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetCalendarByID retrieves a calendar by its ID
func (r *CalendarRepositoryImpl) GetCalendarByID(ctx context.Context, id int64) (*CalendarDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id, name, description, type, is_active, created_at, updated_at, created_by, updated_by
		FROM calendars
		WHERE id = ?
	`

	var calendar CalendarDTO
	err = db.QueryRowContext(ctx, query, id).Scan(
		&calendar.ID, &calendar.Name, &calendar.Description, &calendar.Type, &calendar.IsActive,
		&calendar.CreatedAt, &calendar.UpdatedAt, &calendar.CreatedBy, &calendar.UpdatedBy)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "Calendar not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return &calendar, nil
}

// GetCalendarList retrieves calendars based on query conditions
func (r *CalendarRepositoryImpl) GetCalendarList(ctx context.Context, conditions []commonStorage.QueryCondition) ([]*CalendarDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id, name, description, type, is_active, created_at, updated_at, created_by, updated_by
		FROM calendars
	`

	query, args := commonStorage.BuildQuery(query, conditions...)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var calendars []*CalendarDTO
	for rows.Next() {
		var calendar CalendarDTO
		err := rows.Scan(
			&calendar.ID, &calendar.Name, &calendar.Description, &calendar.Type, &calendar.IsActive,
			&calendar.CreatedAt, &calendar.UpdatedAt, &calendar.CreatedBy, &calendar.UpdatedBy)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		calendars = append(calendars, &calendar)
	}

	return calendars, nil
}

// AddWorkingHours adds working hours for a calendar
func (r *CalendarRepositoryImpl) AddWorkingHours(ctx context.Context, workingHours *CalendarWorkingHoursDTO) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		INSERT INTO calendar_working_hours (calendar_id, day_of_week, start_time, end_time, created_at, updated_at, created_by, updated_by)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		start_time = VALUES(start_time), end_time = VALUES(end_time), updated_at = VALUES(updated_at), updated_by = VALUES(updated_by)
	`

	result, err := db.ExecContext(ctx, query,
		workingHours.CalendarID, workingHours.DayOfWeek, workingHours.StartTime, workingHours.EndTime,
		workingHours.CreatedAt, workingHours.UpdatedAt, workingHours.CreatedBy, workingHours.UpdatedBy)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// BatchAddWorkingHours adds multiple working hours in a transaction
func (r *CalendarRepositoryImpl) BatchAddWorkingHours(ctx context.Context, tx *sql.Tx, workingHours []*CalendarWorkingHoursDTO) error {
	query := `
		INSERT INTO calendar_working_hours (calendar_id, day_of_week, start_time, end_time, created_at, updated_at, created_by, updated_by)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		start_time = VALUES(start_time), end_time = VALUES(end_time), updated_at = VALUES(updated_at), updated_by = VALUES(updated_by)
	`

	for _, wh := range workingHours {
		_, err := tx.ExecContext(ctx, query,
			wh.CalendarID, wh.DayOfWeek, wh.StartTime, wh.EndTime,
			wh.CreatedAt, wh.UpdatedAt, wh.CreatedBy, wh.UpdatedBy)
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
	}

	return nil
}

// UpdateWorkingHours updates existing working hours
func (r *CalendarRepositoryImpl) UpdateWorkingHours(ctx context.Context, workingHours *CalendarWorkingHoursDTO) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		UPDATE calendar_working_hours 
		SET start_time = ?, end_time = ?, updated_at = ?, updated_by = ?
		WHERE id = ?
	`

	_, err = db.ExecContext(ctx, query,
		workingHours.StartTime, workingHours.EndTime, workingHours.UpdatedAt, workingHours.UpdatedBy, workingHours.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// BatchUpdateWorkingHours updates multiple working hours in a transaction
func (r *CalendarRepositoryImpl) BatchUpdateWorkingHours(ctx context.Context, tx *sql.Tx, workingHours []*CalendarWorkingHoursDTO) error {
	query := `
		UPDATE calendar_working_hours 
		SET start_time = ?, end_time = ?, updated_at = ?, updated_by = ?
		WHERE id = ?
	`

	for _, wh := range workingHours {
		_, err := tx.ExecContext(ctx, query,
			wh.StartTime, wh.EndTime, wh.UpdatedAt, wh.UpdatedBy, wh.ID)
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
	}

	return nil
}

// DeleteWorkingHours deletes working hours by ID
func (r *CalendarRepositoryImpl) DeleteWorkingHours(ctx context.Context, id int64) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `DELETE FROM calendar_working_hours WHERE id = ?`

	_, err = db.ExecContext(ctx, query, id)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// BatchDeleteWorkingHours deletes multiple working hours in a transaction
func (r *CalendarRepositoryImpl) BatchDeleteWorkingHours(ctx context.Context, tx *sql.Tx, ids []int64) error {
	query := `DELETE FROM calendar_working_hours WHERE id = ?`

	for _, id := range ids {
		_, err := tx.ExecContext(ctx, query, id)
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
	}

	return nil
}

// GetWorkingHoursByCalendarID retrieves working hours for a calendar
func (r *CalendarRepositoryImpl) GetWorkingHoursByCalendarID(ctx context.Context, calendarID int64) ([]*CalendarWorkingHoursDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id, calendar_id, day_of_week, start_time, end_time, created_at, updated_at, created_by, updated_by
		FROM calendar_working_hours
		WHERE calendar_id = ?
		ORDER BY day_of_week
	`

	rows, err := db.QueryContext(ctx, query, calendarID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var workingHours []*CalendarWorkingHoursDTO
	for rows.Next() {
		var wh CalendarWorkingHoursDTO
		err := rows.Scan(
			&wh.ID, &wh.CalendarID, &wh.DayOfWeek, &wh.StartTime, &wh.EndTime,
			&wh.CreatedAt, &wh.UpdatedAt, &wh.CreatedBy, &wh.UpdatedBy)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		workingHours = append(workingHours, &wh)
	}

	return workingHours, nil
}

// AddHoliday adds a holiday for a calendar
func (r *CalendarRepositoryImpl) AddHoliday(ctx context.Context, holiday *CalendarHolidayDTO) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		INSERT INTO calendar_holidays (calendar_id, holiday_date, name, description, created_at, updated_at, created_by, updated_by)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := db.ExecContext(ctx, query,
		holiday.CalendarID, holiday.HolidayDate, holiday.Name, holiday.Description,
		holiday.CreatedAt, holiday.UpdatedAt, holiday.CreatedBy, holiday.UpdatedBy)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// AddHolidayRange adds multiple holidays for a date range
func (r *CalendarRepositoryImpl) AddHolidayRange(ctx context.Context, calendarID int64, startDate, endDate time.Time, name, description string, userID int64) ([]int64, error) {
	var ids []int64

	// Iterate through each day in the range
	currentDate := startDate
	for !currentDate.After(endDate) {
		holiday := &CalendarHolidayDTO{
			CalendarID:  calendarID,
			HolidayDate: currentDate,
			Name:        name,
			Description: sql.NullString{String: description, Valid: description != ""},
			CreatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
			UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
			CreatedBy:   sql.NullInt64{Int64: userID, Valid: true},
			UpdatedBy:   sql.NullInt64{Int64: userID, Valid: true},
		}

		id, err := r.AddHoliday(ctx, holiday)
		if err != nil {
			// If there's an error, we might want to continue with other dates
			// or return the error. For now, let's continue and log the error.
			continue
		}

		ids = append(ids, id)
		currentDate = currentDate.AddDate(0, 0, 1) // Add one day
	}

	return ids, nil
}

// UpdateHoliday updates an existing holiday
func (r *CalendarRepositoryImpl) UpdateHoliday(ctx context.Context, holiday *CalendarHolidayDTO) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		UPDATE calendar_holidays
		SET holiday_date = ?, name = ?, description = ?, updated_at = ?, updated_by = ?
		WHERE id = ?
	`

	_, err = db.ExecContext(ctx, query,
		holiday.HolidayDate, holiday.Name, holiday.Description, holiday.UpdatedAt, holiday.UpdatedBy, holiday.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// UpdateHolidayRange updates a holiday to span multiple days
func (r *CalendarRepositoryImpl) UpdateHolidayRange(ctx context.Context, holidayID int64, name, description string, userID int64) ([]int64, error) {
	// First, get the original holiday to find the calendar ID
	originalHoliday, err := r.GetHolidayByID(ctx, holidayID)
	if err != nil {
		return nil, err
	}

	// Delete the original holiday
	err = r.DeleteHoliday(ctx, holidayID)
	if err != nil {
		return nil, err
	}

	holiday := &CalendarHolidayDTO{
		CalendarID:  originalHoliday.CalendarID,
		HolidayDate: originalHoliday.HolidayDate,
		Name:        name,
		Description: sql.NullString{String: description, Valid: description != ""},
		CreatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:   sql.NullInt64{Int64: userID, Valid: true},
		UpdatedBy:   sql.NullInt64{Int64: userID, Valid: true},
	}

	// Create new holiday for the date range
	id, err := r.AddHoliday(ctx, holiday)
	if err != nil {
		return nil, err
	}

	return []int64{id}, nil
}

// GetHolidayByID is a helper method to get a holiday by ID
func (r *CalendarRepositoryImpl) GetHolidayByID(ctx context.Context, id int64) (*CalendarHolidayDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id, calendar_id, holiday_date, name, description, created_at, updated_at, created_by, updated_by
		FROM calendar_holidays
		WHERE id = ?
	`

	var holiday CalendarHolidayDTO
	err = db.QueryRowContext(ctx, query, id).Scan(
		&holiday.ID, &holiday.CalendarID, &holiday.HolidayDate, &holiday.Name, &holiday.Description,
		&holiday.CreatedAt, &holiday.UpdatedAt, &holiday.CreatedBy, &holiday.UpdatedBy)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "Holiday not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return &holiday, nil
}

// DeleteHoliday deletes a holiday by ID
func (r *CalendarRepositoryImpl) DeleteHoliday(ctx context.Context, id int64) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `DELETE FROM calendar_holidays WHERE id = ?`

	_, err = db.ExecContext(ctx, query, id)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetHolidaysByCalendarID retrieves holidays for a calendar
func (r *CalendarRepositoryImpl) GetHolidaysByCalendarID(ctx context.Context, conditions []commonStorage.QueryCondition) ([]*CalendarHolidayDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id, calendar_id, holiday_date, name, description, created_at, updated_at, created_by, updated_by
		FROM calendar_holidays
	`

	query, args := commonStorage.BuildQuery(query, conditions...)

	rows, err := db.QueryContext(ctx, query, args)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var holidays []*CalendarHolidayDTO
	for rows.Next() {
		var holiday CalendarHolidayDTO
		err := rows.Scan(
			&holiday.ID, &holiday.CalendarID, &holiday.HolidayDate, &holiday.Name, &holiday.Description,
			&holiday.CreatedAt, &holiday.UpdatedAt, &holiday.CreatedBy, &holiday.UpdatedBy)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		holidays = append(holidays, &holiday)
	}

	return holidays, nil
}

// GetHolidaysByDateRange retrieves holidays within a date range for a calendar
func (r *CalendarRepositoryImpl) GetHolidaysByDateRange(ctx context.Context, calendarID int64, startDate, endDate time.Time) ([]*CalendarHolidayDTO, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id, calendar_id, holiday_date, name, description, created_at, updated_at, created_by, updated_by
		FROM calendar_holidays
		WHERE calendar_id = ? AND holiday_date >= ? AND holiday_date <= ?
		ORDER BY holiday_date
	`

	rows, err := db.QueryContext(ctx, query, calendarID, startDate, endDate)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var holidays []*CalendarHolidayDTO
	for rows.Next() {
		var holiday CalendarHolidayDTO
		err := rows.Scan(
			&holiday.ID, &holiday.CalendarID, &holiday.HolidayDate, &holiday.Name, &holiday.Description,
			&holiday.CreatedAt, &holiday.UpdatedAt, &holiday.CreatedBy, &holiday.UpdatedBy)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}
		holidays = append(holidays, &holiday)
	}

	return holidays, nil
}

// AssignCalendarToElement assigns a calendar to an element
func (r *CalendarRepositoryImpl) AssignCalendarToElement(ctx context.Context, elementID, calendarID int64, userID int64) error {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		INSERT INTO element_calendars (element_id, calendar_id, created_at, updated_at, created_by, updated_by)
		VALUES (?, ?, NOW(), NOW(), ?, ?)
		ON DUPLICATE KEY UPDATE
		calendar_id = VALUES(calendar_id), updated_at = VALUES(updated_at), updated_by = VALUES(updated_by)
	`

	_, err = db.ExecContext(ctx, query, elementID, calendarID, userID, userID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetCalendarIDForElement retrieves the calendar ID assigned to an element
func (r *CalendarRepositoryImpl) GetCalendarIDForElement(ctx context.Context, elementID int64) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	query := `
		SELECT calendar_id
		FROM element_calendars
		WHERE element_id = ?
		LIMIT 1
	`

	var calendarID int64
	err = db.QueryRowContext(ctx, query, elementID).Scan(&calendarID)
	if err != nil {
		if err == sql.ErrNoRows {
			// If no specific calendar is assigned, return the default calendar
			return r.GetDefaultCalendarID(ctx)
		}
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return calendarID, nil
}

// GetDefaultCalendarID retrieves the default calendar ID (first active calendar)
func (r *CalendarRepositoryImpl) GetDefaultCalendarID(ctx context.Context) (int64, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	query := `
		SELECT id
		FROM calendars
		WHERE is_active = TRUE
		ORDER BY id
		LIMIT 1
	`

	var calendarID int64
	err = db.QueryRowContext(ctx, query).Scan(&calendarID)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, errorwrapper.Error(apiError.ResourceNotFound, "No active calendar found")
		}
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return calendarID, nil
}

// GetCalendarInfo retrieves complete calendar information including working hours and holidays
func (r *CalendarRepositoryImpl) GetCalendarInfo(ctx context.Context, calendarID int64) (*CalendarInfo, error) {
	// Get calendar details
	calendar, err := r.GetCalendarByID(ctx, calendarID)
	if err != nil {
		return nil, err
	}

	// Get working hours
	workingHours, err := r.GetWorkingHoursByCalendarID(ctx, calendarID)
	if err != nil {
		return nil, err
	}

	// Get holidays
	var conditions []commonStorage.QueryCondition
	conditions = append(conditions, commonStorage.EqualTo("calendar_id", calendarID))
	conditions = append(conditions, commonStorage.AscendingOrder("holiday_date"))
	conditions = append(conditions, commonStorage.EqualTo("YEAR(holiday_date)", fmt.Sprintf("%d", time.Now().Year())))

	holidays, err := r.GetHolidaysByCalendarID(ctx, conditions)
	if err != nil {
		return nil, err
	}

	return &CalendarInfo{
		Calendar:     calendar,
		WorkingHours: workingHours,
		Holidays:     holidays,
	}, nil
}

func (r *CalendarRepositoryImpl) BeginTransaction(ctx context.Context) (*sql.Tx, error) {
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, r.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, err.Error())
	}

	tx, err := db.Begin()
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return tx, nil
}

func (r *CalendarRepositoryImpl) CommitTransaction(ctx context.Context, tx *sql.Tx) error {
	err := tx.Commit()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

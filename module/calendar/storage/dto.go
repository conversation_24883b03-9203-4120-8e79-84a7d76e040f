// Package storage provide the data access layer for calendar module
package storage

import (
	"database/sql"
	"time"
)

// CalendarDTO represents the calendar data transfer object
type CalendarD<PERSON> struct {
	ID          int64          `json:"id" db:"id"`
	Name        string         `json:"name" db:"name"`
	Description sql.NullString `json:"description" db:"description"`
	Type        string         `json:"type" db:"type"`
	IsActive    bool           `json:"is_active" db:"is_active"`
	CreatedAt   sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt   sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy   sql.NullInt64  `json:"created_by" db:"created_by"`
	UpdatedBy   sql.NullInt64  `json:"updated_by" db:"updated_by"`
}

// CalendarWorkingHoursDTO represents the calendar working hours data transfer object
type CalendarWorkingHoursDTO struct {
	ID         int64         `json:"id" db:"id"`
	CalendarID int64         `json:"calendar_id" db:"calendar_id"`
	DayOfWeek  int32         `json:"day_of_week" db:"day_of_week"` // 0=Sunday, 1=Monday, etc.
	StartTime  string        `json:"start_time" db:"start_time"`   // "09:00:00" format
	EndTime    string        `json:"end_time" db:"end_time"`       // "17:30:00" format
	CreatedAt  sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt  sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy  sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy  sql.NullInt64 `json:"updated_by" db:"updated_by"`
}

// GetStartTimeAsTime parses the start time string to time.Time for calculations
func (w *CalendarWorkingHoursDTO) GetStartTimeAsTime() (time.Time, error) {
	return time.Parse("15:04:05", w.StartTime)
}

// GetEndTimeAsTime parses the end time string to time.Time for calculations
func (w *CalendarWorkingHoursDTO) GetEndTimeAsTime() (time.Time, error) {
	return time.Parse("15:04:05", w.EndTime)
}

// SetStartTimeFromTime sets the start time from a time.Time value
func (w *CalendarWorkingHoursDTO) SetStartTimeFromTime(t time.Time) {
	w.StartTime = t.Format("15:04:05")
}

// SetEndTimeFromTime sets the end time from a time.Time value
func (w *CalendarWorkingHoursDTO) SetEndTimeFromTime(t time.Time) {
	w.EndTime = t.Format("15:04:05")
}

// CalendarHolidayDTO represents the calendar holiday data transfer object
type CalendarHolidayDTO struct {
	ID          int64          `json:"id" db:"id"`
	CalendarID  int64          `json:"calendar_id" db:"calendar_id"`
	HolidayDate time.Time      `json:"holiday_date" db:"holiday_date"`
	Name        string         `json:"name" db:"name"`
	Description sql.NullString `json:"description" db:"description"`
	CreatedAt   sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt   sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy   sql.NullInt64  `json:"created_by" db:"created_by"`
	UpdatedBy   sql.NullInt64  `json:"updated_by" db:"updated_by"`
}

// ElementCalendarDTO represents the element to calendar mapping data transfer object
type ElementCalendarDTO struct {
	ElementID  int64         `json:"element_id" db:"element_id"`
	CalendarID int64         `json:"calendar_id" db:"calendar_id"`
	CreatedAt  sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt  sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy  sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy  sql.NullInt64 `json:"updated_by" db:"updated_by"`
}

// WorkingHoursInfo represents working hours information for a specific day
type WorkingHoursInfo struct {
	DayOfWeek int32     `json:"day_of_week"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// CalendarInfo represents complete calendar information including working hours and holidays
type CalendarInfo struct {
	Calendar     *CalendarDTO               `json:"calendar"`
	WorkingHours []*CalendarWorkingHoursDTO `json:"working_hours"`
	Holidays     []*CalendarHolidayDTO      `json:"holidays"`
}

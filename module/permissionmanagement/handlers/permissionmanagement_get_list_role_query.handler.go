package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
)

// GetListRoleQuery handles the new GET /api/v1/roles/list endpoint with flat query parameters
// This method converts flat parameters to the nested filter structure and delegates to GetListRole
func (p *PermissionManagementService) GetListRoleQuery(ctx context.Context, req *api.GetListRoleQueryRequest) (*api.GetListRoleResponse, error) {
	// Convert flat query parameters to nested filter structure
	convertedReq := &api.GetListRoleRequest{
		SearchKey: req.SearchKey,
		Offset:    req.Offset,
		Limit:     req.Limit,
		SortBy:    req.SortBy,
	}

	// TODO: When FE fully migrated from the deprecated POST endpoint, remove this code and move to business logic
	// Convert flat status parameters to filter structure
	var filters []api.Filter
	if req.StatusIds != nil && len(req.StatusIds) > 0 {
		statusValues := make([]interface{}, len(req.StatusIds))
		for i, status := range req.StatusIds {
			// Convert int64 to string as expected by the original filter logic
			statusValues[i] = fmt.Sprintf("%d", status)
		}

		filters = append(filters, api.Filter{
			Column: "status",
			Value:  statusValues,
		})
	}

	convertedReq.Filter = filters

	// Delegate to the existing GetListRole handler
	return p.GetListRole(ctx, convertedReq)
}

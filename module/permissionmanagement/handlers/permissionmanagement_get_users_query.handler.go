package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
)

// GetUsersQuery handles the new GET /api/v1/users endpoint with flat query parameters
// This method converts flat parameters to the nested filter structure and delegates to GetUsers
func (p *PermissionManagementService) GetUsersQuery(ctx context.Context, req *api.GetUsersQueryRequest) (*api.GetUsersResponse, error) {
	// Convert flat query parameters to nested filter structure
	convertedReq := &api.GetUsersRequest{
		SearchKey: req.SearchKey,
		Offset:    req.Offset,
		Limit:     req.Limit,
		SortBy:    req.SortBy,
	}

	// TODO: When FE fully migrated from the deprecated POST endpoint, remove this code and move to business logic
	// Convert flat status parameters to filter structure
	var filters []api.Filter
	if req.StatusIds != nil && len(req.StatusIds) > 0 {
		statusValues := make([]interface{}, len(req.StatusIds))
		for i, status := range req.StatusIds {
			statusValues[i] = status
		}

		filters = append(filters, api.Filter{
			Column: "status",
			Value:  statusValues,
		})
	}

	// Convert flat roleId parameters to filter structure
	if req.RoleIds != nil && len(req.RoleIds) > 0 {
		roleValues := make([]interface{}, len(req.RoleIds))
		for i, role := range req.RoleIds {
			roleValues[i] = role
		}

		filters = append(filters, api.Filter{
			Column: "role_id",
			Value:  roleValues,
		})
	}

	convertedReq.Filter = filters

	// Delegate to the existing GetUsers handler
	return p.GetUsers(ctx, convertedReq)
}

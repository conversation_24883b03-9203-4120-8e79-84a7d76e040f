package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// UpdateUser ...
func UpdateUser(ctx context.Context, db *sql.DB, r *UserDTO) error {
	var query = `UPDATE users
	SET updated_at = ?, updated_by = ?, name = ?, email = ?, status = ?
	WHERE user_id = ?`

	result, err := db.ExecContext(ctx, query, r.UpdatedAt, r.UpdatedBy, r.Name, r.Email, r.Status, r.UserID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rows != 1 {
		return errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("expected to affect 1 row, affected %d", rows))
	}

	return nil
}

// UpdateUserStatus ...
func UpdateUserStatus(ctx context.Context, db *sql.DB, status int64, userID string, creatorID int64) error {
	var query = `UPDATE users
	SET status = ?, updated_at = now(), updated_by = ?
	WHERE user_id = ?`

	result, err := db.ExecContext(ctx, query, status, creatorID, userID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rows != 1 {
		return errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("expected to affect 1 row, affected %d", rows))
	}

	return nil
}

// UpdateUserExpiryToken ...
func UpdateUserExpiryToken(ctx context.Context, db *sql.DB, refreshToken, userID string, ID int64) error {
	var query = `UPDATE users
	SET refresh_token = ?,
	    updated_at = now(),
	    updated_by = ?
	WHERE user_id = ?`

	result, err := db.ExecContext(ctx, query, refreshToken, ID, userID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	rows, err := result.RowsAffected()
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	if rows != 1 {
		return errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("expected to affect 1 row, affected %d", rows))
	}

	return nil
}

// GetUserByUserID ...
func GetUserByUserID(ctx context.Context, db *sql.DB, userID string) (*UserDTO, error) {
	var query = `SELECT id, name, email, password, created_at, updated_at, created_by, updated_by, status, user_id, refresh_token FROM users WHERE user_id = ?`

	var user UserDTO
	err := db.QueryRowContext(ctx, query, userID).Scan(&user.ID, &user.Name, &user.Email, &user.Password, &user.CreatedAt, &user.UpdatedAt, &user.CreatedBy, &user.UpdatedBy, &user.Status, &user.UserID, &user.RefreshToken)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "User is not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return &user, nil
}

// UpdateUserPasswordToken ...
func UpdateUserPasswordToken(ctx context.Context, db *sql.DB, password, userID string, ID int64) error {
	var query = `UPDATE users
	SET password = ?,
	    updated_at = now(),
	    updated_by = ?
	WHERE user_id = ?`

	_, err := db.ExecContext(ctx, query, password, ID, userID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetUserList ...
func GetUserList(ctx context.Context, db *sql.DB, conditions []commonStorage.QueryCondition, roles []string) ([]UserListDTO, error) {
	query := `SELECT DISTINCT r.id, r.name, r.status, r.email, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by, r.user_id FROM users r
    LEFT JOIN users u ON r.created_by = u.id 
	LEFT JOIN users u2 ON r.updated_by = u2.id	
    LEFT JOIN (
			SELECT DISTINCT user_id FROM users_roles`

	var args []any
	if len(roles) > 0 {
		query = `SELECT DISTINCT r.id, r.name, r.status, r.email, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by, r.user_id FROM users r
    LEFT JOIN users u ON r.created_by = u.id 
	LEFT JOIN users u2 ON r.updated_by = u2.id	
	INNER JOIN (
			SELECT DISTINCT user_id FROM users_roles WHERE role_id IN (`
		for i, role := range roles {
			if i > 0 {
				query += ", "
			}
			query += "?"
			args = append(args, role)
		}
		query += `)`
	}

	query += `) AS ur ON r.id = ur.user_id`
	query, additionalArgs := commonStorage.BuildQuery(query, conditions...)
	args = append(args, additionalArgs...)
	var users []UserListDTO
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return users, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	for rows.Next() {
		var user UserListDTO
		scanErr := rows.Scan(&user.ID,
			&user.Name,
			&user.Status,
			&user.Email,
			&user.CreatedAt,
			&user.UpdatedAt,
			&user.CreatedBy,
			&user.UpdatedBy,
			&user.UserID)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return users, nil
}

// GetCountUsers ...
func GetCountUsers(ctx context.Context, db *sql.DB, conditions []commonStorage.QueryCondition, roles []string) (int64, error) {
	query := `SELECT DISTINCT count(*) FROM users r
    LEFT JOIN (
        SELECT DISTINCT user_id FROM users_roles`

	var args []any
	if len(roles) > 0 {
		query = `SELECT DISTINCT count(*) FROM users r
    	INNER JOIN (
        	SELECT DISTINCT user_id FROM users_roles WHERE role_id IN (`
		for i, role := range roles {
			if i > 0 {
				query += ", "
			}
			query += "?"
			args = append(args, role)
		}
		query += `)`
	}

	query += `) AS ur ON r.id = ur.user_id`
	query, additionalArgs := commonStorage.BuildQuery(query, conditions...)
	args = append(args, additionalArgs...)
	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return count, nil
}

// GetUsersByRoleID ...
func GetUsersByRoleID(ctx context.Context, db *sql.DB, roleID int64) ([]*UserDTO, error) {
	var query = `
	SELECT id, name, email, password, created_at, updated_at, created_by, updated_by, status, user_id, refresh_token FROM users
	WHERE id IN (
		SELECT user_id from users_roles
		WHERE role_id = ?
	);
	`

	// Execute the query
	rows, err := db.QueryContext(ctx, query, roleID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var users []*UserDTO
	for rows.Next() {
		var user UserDTO
		scanErr := rows.Scan(&user.ID,
			&user.Name,
			&user.Email,
			&user.Password,
			&user.CreatedAt,
			&user.UpdatedAt,
			&user.CreatedBy,
			&user.UpdatedBy,
			&user.Status,
			&user.UserID,
			&user.RefreshToken)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		users = append(users, &user)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return users, nil
}

// CreateUser ...
//
//nolint:dupl
func CreateUser(ctx context.Context, db *sql.DB, user *UserDTO) (id int64, err error) {
	var query = `INSERT INTO users (name, email, password, status, user_id, created_at, updated_at, created_by, updated_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
	result, err := db.ExecContext(ctx, query, user.Name, user.Email, user.Password, user.Status, user.UserID, user.CreatedAt, user.UpdatedAt, user.CreatedBy, user.UpdatedBy)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// GetUserByEmail ...
// nolint:dupl
func GetUserByEmail(ctx context.Context, db *sql.DB, email string) (*UserDTO, error) {
	var query = `SELECT id, name, email, password, status, user_id, created_at, updated_at, created_by, updated_by FROM users WHERE email = ?`

	var user UserDTO
	err := db.QueryRowContext(ctx, query, email).Scan(&user.ID, &user.Name, &user.Email, &user.Password, &user.Status, &user.UserID, &user.CreatedAt, &user.UpdatedAt, &user.CreatedBy, &user.UpdatedBy)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("Email not found, err: %s", err.Error()))
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return &user, nil
}

func GetAllUserNames(ctx context.Context, db *sql.DB) ([]*UserDTO, error) {
	var query = `SELECT DISTINCT(name), id FROM users order by name ASC`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var users []*UserDTO
	for rows.Next() {
		var user UserDTO
		scanErr := rows.Scan(
			&user.Name,
			&user.ID,
		)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		users = append(users, &user)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return users, nil
}

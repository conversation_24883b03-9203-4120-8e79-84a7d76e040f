package storage

import (
	"context"
	"database/sql"
	"errors"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateRole ...
func CreateRole(ctx context.Context, db *sql.DB, p *RoleDTO) (id int64, err error) {
	var query = `INSERT INTO roles (name, status, created_at, created_by, updated_at, updated_by) VALUES (?, ?, ?, ?, ?, ?)`

	result, err := db.ExecContext(ctx, query, p.Name, p.Status, p.CreatedAt, p.CreatedBy, p.UpdatedAt, p.UpdatedBy)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.<PERSON>rror())
	}

	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// UpdateRole ...
func UpdateRole(ctx context.Context, db *sql.DB, r *RoleDTO) error {
	var query = `UPDATE roles SET name = ?, status = ?, updated_at = ?, updated_by = ? WHERE id = ?`

	_, err := db.ExecContext(ctx, query, r.Name, r.Status, r.UpdatedAt, r.UpdatedBy, r.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetRoleList ...
func GetRoleList(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]*RoleListDTO, error) {
	var query = `
	select r.id, r.name, r.status, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by from roles r 
    left join users u ON r.created_by = u.id 
	left join users u2 ON r.updated_by = u2.id`
	query, args := storage.BuildQuery(query, conditions...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var roles []*RoleListDTO
	for rows.Next() {
		var role RoleListDTO
		scanErr := rows.Scan(&role.ID,
			&role.Name,
			&role.Status,
			&role.CreatedAt,
			&role.UpdatedAt,
			&role.CreatedBy,
			&role.UpdatedBy)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		roles = append(roles, &role)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return roles, nil
}

// GetCountRoles ...
func GetCountRoles(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (int64, error) {
	var query = `SELECT count(*) FROM roles`
	query, args := storage.BuildQuery(query, conditions...)
	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return count, nil
}

// GetUserRole ...
func GetUserRole(ctx context.Context, db *sql.DB, userID int64) ([]*RoleDTO, error) {
	var query = `SELECT r.ID, r.name FROM roles r 
		INNER JOIN users_roles ur ON r.id = ur.role_id AND r.status = 1
		WHERE ur.user_id = ?`

	var roles []*RoleDTO
	rows, err := db.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	for rows.Next() {
		var role RoleDTO
		scanErr := rows.Scan(&role.ID,
			&role.Name)
		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		roles = append(roles, &role)
	}

	if err = rows.Err(); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, err.Error())
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return roles, nil
}

// GetRoleByID ...
//
// nolint: dupl
func GetRoleByID(ctx context.Context, db *sql.DB, id int64) (*RoleDTO, error) {
	var query = `SELECT id, name, status, created_at, updated_at, created_by, updated_by from roles where id = ?`

	var role RoleDTO
	err := db.QueryRowContext(ctx, query, id).Scan(&role.ID,
		&role.Name,
		&role.Status,
		&role.CreatedAt,
		&role.UpdatedAt,
		&role.CreatedBy,
		&role.UpdatedBy)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "role is not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return &role, nil
}

// UpdateRoleStatus ...
func UpdateRoleStatus(ctx context.Context, db *sql.DB, r *RoleDTO) error {
	var query = `UPDATE roles SET status = ?, updated_at = ?, updated_by = ? WHERE id = ?`

	_, err := db.ExecContext(ctx, query, r.Status, r.UpdatedAt, r.UpdatedBy, r.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetRoleInfoByID ...
//
// nolint: dupl
func GetRoleInfoByID(ctx context.Context, db *sql.DB, id int64) (*RoleListDTO, error) {
	var query = `
	WITH selected_role AS (
	    SELECT * from roles where id = ?
	)
	select r.id, r.name, r.status, r.created_at, r.updated_at, u.name as created_by, u2.name as updated_by from selected_role r 
    left join users u ON r.created_by = u.id 
	left join users u2 ON r.updated_by = u2.id
	`

	var role RoleListDTO
	err := db.QueryRowContext(ctx, query, id).Scan(&role.ID,
		&role.Name,
		&role.Status,
		&role.CreatedAt,
		&role.UpdatedAt,
		&role.CreatedBy,
		&role.UpdatedBy)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "role is not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return &role, nil
}

// GetAllActiveRoles ...
func GetAllActiveRoles(ctx context.Context, db *sql.DB) ([]RoleDTO, error) {
	var query = `
		SELECT id, name, status, created_at, created_by, updated_at, updated_by FROM roles
		where status = 1
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var roles []RoleDTO
	for rows.Next() {
		var role RoleDTO
		scanErr := rows.Scan(&role.ID,
			&role.Name,
			&role.Status,
			&role.CreatedAt,
			&role.CreatedBy,
			&role.UpdatedAt,
			&role.UpdatedBy)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		roles = append(roles, role)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return roles, nil
}

// GetRolesByElementCode ...
func GetRolesByElementCode(ctx context.Context, db *sql.DB, elementCode string, conditions []storage.QueryCondition) ([]*RoleDTO, error) {
	var query = `
	WITH element AS (
		SELECT e.id from elements e
		WHERE e.code = ? AND e.status = 1
	),
	element_roles as (
		SELECT DISTINCT ur.role_id from roles_elements_permissions ur
		WHERE ur.element_id IN (select id from element)
	)
	SELECT r.id, r.name, r.status FROM roles r
	INNER JOIN element_roles er
	ON r.id = er.role_id AND r.status = 1
	`

	args := []any{elementCode}
	query, additionalArgs := storage.BuildQuery(query, conditions...)
	args = append(args, additionalArgs...)
	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer func() {
		_ = rows.Close()
	}()

	var roles []*RoleDTO
	for rows.Next() {
		var role RoleDTO
		scanErr := rows.Scan(&role.ID,
			&role.Name,
			&role.Status)

		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		roles = append(roles, &role)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return roles, nil
}

// GetCountRolesByElementCode ...
func GetCountRolesByElementCode(ctx context.Context, db *sql.DB, elementCode string, conditions []storage.QueryCondition) (int64, error) {
	var query = `
	WITH element AS (
		SELECT e.id from elements e
		WHERE e.code = ? AND e.status = 1
	),
	element_roles as (
		SELECT DISTINCT ur.role_id from roles_elements_permissions ur
		WHERE ur.element_id IN (select id from element)
	)
	SELECT count(*) FROM roles r
	INNER JOIN element_roles er
	ON r.id = er.role_id AND r.status = 1
	`

	args := []any{elementCode}
	query, additionalArgs := storage.BuildQuery(query, conditions...)
	args = append(args, additionalArgs...)
	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	return count, nil
}

// GetRolesByIDs fetches multiple roles using a list of IDs
func GetRolesByIDs(ctx context.Context, db *sql.DB, ids []int64) ([]*RoleDTO, error) {
	if len(ids) == 0 {
		return []*RoleDTO{}, nil
	}

	query := `
		SELECT id, name, status, created_at, created_by, updated_at, updated_by
		FROM roles
		WHERE id IN (?` + strings.Repeat(",?", len(ids)-1) + `)
	`

	args := make([]any, len(ids))
	for i, id := range ids {
		args[i] = id
	}

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var roles []*RoleDTO
	for rows.Next() {
		var role RoleDTO
		scanErr := rows.Scan(
			&role.ID,
			&role.Name,
			&role.Status,
			&role.CreatedAt,
			&role.CreatedBy,
			&role.UpdatedAt,
			&role.UpdatedBy,
		)
		if scanErr != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, scanErr.Error())
		}
		roles = append(roles, &role)
	}

	if err = rows.Err(); err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return roles, nil
}

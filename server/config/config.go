// Package config provides the configuration for the application.
package config

import (
	"github.com/myteksi/hystrix-go/hystrix"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/appian"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/chatbot"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/customerportal"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/hedwig"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
)

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	ServicesConfig
	QueuesConfig
	Appian              AppianConfig                  `json:"appianConfig"`
	SchedulerConfig     SchedulerConfig               `json:"scheduler"`
	FileProcessorConfig FileProcessorConfig           `json:"fileProcessor"`
	ServicesCredential  map[string]ServicesCredential `json:"servicesCredential"`
	Locale              Locale                        `json:"locale"`
}

// SchedulerConfig ...
type SchedulerConfig struct {
	WriteOffJob SchedulerConfigDetail `json:"writeOffJob"`
	WaiveOffJob SchedulerConfigDetail `json:"waiveOffJob"`
}

// SchedulerConfigDetail ...
type SchedulerConfigDetail struct {
	CronExpression             string `json:"cronExpression"`
	SchedulerLockDurationInSec int64  `json:"schedulerLockDurationInSec"`
}

// FileProcessorConfig ...
type FileProcessorConfig struct {
	WriteOffFileProcessorConfig FileProcessorConfigDetail `json:"writeOffFileProcessorConfig"`
	WaiveOffFileProcessorConfig FileProcessorConfigDetail `json:"waiveOffFileProcessorConfig"`
}

// FileProcessorConfigDetail ...
type FileProcessorConfigDetail struct {
	MaxRecords int `json:"maxRecords"`
	Timeout    int `json:"timeout"`
	RecordTTL  int `json:"recordTTL"` // TTL for the records in seconds
}

// Locale ...
type Locale struct {
	HomeCountry string `json:"homeCountry"`
}

// ServicesConfig ...
type ServicesConfig struct {
	AccountServiceConfig                  *BackendServiceConfig  `json:"accountServiceConfig"`
	CustomerPortalConfig                  *customerportal.Config `json:"customerPortalConfig"`
	Hedwig                                Hedwig                 `json:"hedwig"`
	LogAuditTrailsRules                   map[string]interface{} `json:"logAuditTrailsRules"`
	PayAuthZConfig                        *BackendServiceConfig  `json:"payAuthZConfig"`
	PaymentOpsTrf                         *BackendServiceConfig  `json:"paymentOpsTrfConfig"`
	LoanCoreKafka                         *KafkaConfig           `json:"loanCoreLocKafkaConfig"`
	DepositsAccountDetailEventKafkaConfig *KafkaConfig           `json:"depositsAccountDetailEventKafkaConfig"`
	RedisConf                             *redis.Config          `json:"redisConfig"`
	RedisAppConfig                        RedisAppConfig         `json:"redisAppConfig"`
	TokenKey                              TokenKey               `json:"tokenKey"`
	Jumpcloud                             *jumpcloud.Config      `json:"jumpcloud"`
	S3Config                              S3Config               `json:"s3"`
	CustomerMasterConfig                  *BackendServiceConfig  `json:"customerMasterConfig"`
	CustomerExperienceConfig              *BackendServiceConfig  `json:"customerExperienceConfig"`
	LendingActionStatusKafkaConfig        *KafkaConfig           `json:"lendingActionStatusKafkaConfig"`
	LendingActionKafkaConfig              *KafkaConfig           `json:"lendingActionKafkaConfig"`
	ChatbotServiceConfig                  *chatbot.Config        `json:"chatbotServiceConfig"`
	ProductMasterConfig                   *BackendServiceConfig  `json:"productMasterConfig"`
	TransactionHistoryConfig              *BackendServiceConfig  `json:"transactionHistoryConfig"`
	GrabIDConfig                          GrabIDConfig           `json:"grabIdConfig"`
	FeatureFlag                           FeatureFlag            `json:"featureFlag"`
	TransactionLimitConfig                *BackendServiceConfig  `json:"transactionLimitConfig"`
	CustomerJournalConfig                 *BackendServiceConfig  `json:"customerJournalConfig"`
	PaymentServiceConfig                  *PaymentServiceConfig  `json:"paymentServiceConfig"`
	CustomerJourneyExperienceConfig       *BackendServiceConfig  `json:"customerJourneyExperienceConfig"`
	LoanAppConfig                         *BackendServiceConfig  `json:"loanAppConfig"`
	LoanExpConfig                         *BackendServiceConfig  `json:"loanExpConfig"`
	LoanCoreConfig                        *BackendServiceConfig  `json:"loanCoreConfig"`
	AmlServiceConfig                      *BackendServiceConfig  `json:"amlServiceConfig"`
	OpsConfig                             *BackendServiceConfig  `json:"ops"`
}

// QueuesConfig ...
type QueuesConfig struct {
	BlockAccountQueue        *SQSConfig `json:"blockAccountQueue"`
	UnblockAccountQueue      *SQSConfig `json:"unblockAccountQueue"`
	AuditTrailQueue          *SQSConfig `json:"auditTrailQueue"`
	UnlinkAccountQueue       *SQSConfig `json:"unlinkAccountQueue"`
	TransferOnBehalfQueue    *SQSConfig `json:"transferOnBehalfQueue"`
	DeactivateLOCQueue       *SQSConfig `json:"deactivateLOCQueue"`
	UpdateAccountStatusQueue *SQSConfig `json:"updateAccountStatusQueue"`
}

// SQSConfig defines the config of SQS.
type SQSConfig struct {
	QueueURL        string `json:"queueURL"`
	AwsRegion       string `json:"awsRegion"`
	DelaySeconds    int64  `json:"delaySeconds"`
	WaitTimeSeconds int64  `json:"waitTimeSeconds"`
	MaxPool         int    `json:"maxPool"`
	SizePool        int    `json:"sizePool"`
	NoWorker        int    `json:"noWorker"`
	Tag             string `json:"tag"`     // for logging purpose
	Enabled         bool   `json:"enabled"` // to enable or disable the queue
}

// BackendServiceConfig ...
type BackendServiceConfig struct {
	ServiceName           string     `json:"serviceName"`
	IsFeatureEnabled      bool       `json:"isFeatureEnabled"`
	BaseURL               string     `json:"baseURL"`
	IsHealthCheckDisabled bool       `json:"isHealthCheckDisabled" default:"false"`
	CircuitBreaker        *CBSetting `json:"circuitBreaker"`
}

// PaymentServiceConfig ...
type PaymentServiceConfig struct {
	PartnerID string `json:"partnerID"`
}

// CBSetting is the circuit breaker configurations for storage
// It adds ErrorHandler to ignore context.Canceled errors.
type CBSetting struct {
	hystrix.CommandConfig
	IgnoredHTTPCode []int `json:"ignoredHTTPCode"`
}

// Hedwig ...
type Hedwig struct {
	ClientConfig                          hedwig.Config     `json:"clientConfig"`
	PushNotificationTemplateMappings      map[string]string `json:"pushNotificationTemplateMappings"`
	EmailNotificationTemplateMappings     map[string]string `json:"emailNotificationTemplateMappings"`
	PushInboxNotificationTemplateMappings map[string]string `json:"pushInboxNotificationTemplateMappings"`
}

// KafkaConfig ...
type KafkaConfig struct {
	Brokers     []string             `json:"brokers"`
	TopicName   string               `json:"topicName"`
	ClusterType string               `json:"clusterType"`
	EnableTLS   bool                 `json:"enableTLS"`
	InitOffset  sndconfig.OffsetType `json:"initOffset"`
	ClientID    string               `json:"clientID"`
	PackageName string               `json:"packageName"`
	DtoName     string               `json:"dtoName"`
	Enable      bool                 `json:"enable"`
}

// StatsDConfig ...
func (a *AppConfig) StatsDConfig() *servus.StatsDConfig {
	return a.StatsD
}

// RedisAppConfig ...
type RedisAppConfig struct {
	ExpiryTimeForRedis int64 `json:"expiryTimeForRedis"`
}

// TokenKey ...
type TokenKey struct {
	AccessTokenKey         string `json:"accessTokenKey"`
	RefreshTokenKey        string `json:"refreshTokenKey"`
	AccessTokenExpiryTime  int    `json:"accessTokenExpiryTime"`
	RefreshTokenExpiryTime int    `json:"refreshTokenExpiryTime"`
}

// S3Config ...
type S3Config struct {
	BucketName       string   `json:"bucketName"`
	BucketURL        string   `json:"bucketUrl,omitempty"`
	Directory        string   `json:"directory,omitempty"`
	AllowedMimeTypes []string `json:"allowedMimeTypes,omitempty"`
	MaxFileSize      int      `json:"maxFileSize,omitempty"`
}

// AppianConfig ...
type AppianConfig struct {
	*appian.Config
}

// GrabIDConfig ...
type GrabIDConfig struct {
	ClientConfig grabid.Config `json:"clientConfig"`
}

// FeatureFlag ...
type FeatureFlag struct {
	UseConcurrentLoginLimit      bool `json:"useConcurrentLoginLimit"`
	UseNativeLogin               bool `json:"useNativeLogin"`
	SkipValidationForDraftTicket bool `json:"skipValidationForDraftTicket"`
}

// ServicesCredential ...
type ServicesCredential struct {
	ClientID     string `json:"clientID"`
	ClientSecret string `json:"clientSecret"`
}

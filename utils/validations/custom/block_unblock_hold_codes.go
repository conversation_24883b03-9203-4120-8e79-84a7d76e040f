package custom

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

// getAllValidHoldCodes returns a map of all valid hold codes (from AllowedHoldCodes and hold code constants)
func getAllValidHoldCodes() map[string]bool {
	validCodes := make(map[string]bool)
	// Add from AllowedHoldCodes
	for k := range constants.AllowedHoldCodes {
		validCodes[string(k)] = true
	}
	// Add from hold code constants
	constCodes := []string{
		constants.HoldCodeBankrupt,
		constants.HoldCodeDeceased,
		constants.HoldCodeMentalIncapacity,
		constants.HoldCodeMissingDevice,
		constants.HoldCodeKillSwitch,
		constants.HoldCodeGarnisheeClawback,
		constants.HoldCodeBankRestricted,
		constants.HoldCodeAdverseNews,
		constants.HoldCodeAmlSanctionHits,
		constants.HoldCodePoliceCourtSeizure,
		constants.HoldCodeMuleAccount,
		constants.HoldCodeFraudActor,
		constants.HoldCodeFraudVictim,
	}
	for _, c := range constCodes {
		validCodes[c] = true
	}
	return validCodes
}

// BlockUnblockHoldCodesValidation is a custom validator for validating hold codes in block/unblock account request
func BlockUnblockHoldCodesValidation(fl validations.FieldLevel) bool {
	// Get the field value as a slice of strings
	holdCodes, ok := fl.Field().Interface().([]string)
	if !ok {
		slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "error getting hold codes")
		return false
	}

	// Check if the slice is empty
	if len(holdCodes) == 0 {
		slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "hold codes is empty")
		return false
	}

	var checkDuplicate = make(map[string]bool)
	validCodes := getAllValidHoldCodes()
	for _, holdCode := range holdCodes {
		// forbid empty hold code
		if holdCode == "" {
			slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "hold code is empty")
			return false
		}

		// forbid invalid hold code
		if !validCodes[holdCode] {
			slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "invalid hold code")
			return false
		}

		// forbid duplicate hold code
		if checkDuplicate[holdCode] {
			slog.FromContext(context.Background()).Error("BlockUnblockHoldCodesValidation", "duplicate hold code")
			return false
		}
		checkDuplicate[holdCode] = true
	}
	return true
}

package custom

import (
	"context"
	"fmt"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
)

// TicketFieldsValidationStrategy defines the interface for country-specific ticket validation
type TicketFieldsValidationStrategy interface {
	Validate(ctx context.Context, req *api.CreateTicketRequest) error
}

// DefaultFieldsValidationStrategy implements the default validation strategy
type DefaultFieldsValidationStrategy struct{}

func (d *DefaultFieldsValidationStrategy) Validate(ctx context.Context, req *api.CreateTicketRequest) error {
	return validations.ValidateRequest(req)
}

// MYTicketFieldsValidationStrategy implements validation for Malaysia
type SpecialTicketFieldsValidationStrategy struct{}

func (m *SpecialTicketFieldsValidationStrategy) Validate(ctx context.Context, req *api.CreateTicketRequest) error {

	var errMsgs []string
	var captureSection map[string]interface{}

	if req.ElementID <= 0 {
		errMsgs = append(errMsgs, "elementID is required and must be greater than 0")
	}
	if req.PriorityID <= 0 {
		errMsgs = append(errMsgs, "priorityID is required and must be greater than 0")
	}
	if req.TicketRequestorID <= 0 {
		errMsgs = append(errMsgs, "ticketRequestorID is required and must be greater than 0")
	}

	if req.Data.Capture != nil {
		captureSection = req.Data.Capture.(map[string]interface{})
		moduleName, ok := captureSection["moduleName"].(string)
		if !ok {
			// This can happen if the field is missing or not a string.
			errMsgs = append(errMsgs, "validation failed: 'moduleName' field not found or is not a string")
		}

		// Perform the final validation check
		if moduleName == "" {
			errMsgs = append(errMsgs, "validation failed: moduleName is empty")
		}

		description, _ := captureSection["description"].(string)
		if description != "" && len(description) > 1000 {
			errMsgs = append(errMsgs, "validation failed: description is exceeds 1000 characters")
		}
	}

	if len(errMsgs) > 0 {
		return errorwrapper.Error(apiError.BadRequest, strings.Join(errMsgs, " or "))
	}

	return nil
}

// create singleton resource
var ticketValidationContext *TicketValidationContext

// InitTicketFieldsValidationStrategy to set up ticket validation strategy based on country
func InitTicketFieldsValidationStrategy(country string) {
	var ticketFieldsValidationStrategy TicketFieldsValidationStrategy
	switch country {
	case constants.HomeCountryMY, constants.HomeCountrySG:
		slog.FromContext(context.Background()).Info("SetCountryStrategy", fmt.Sprintf("using Malaysia validation strategy for country: %s", country))
		ticketFieldsValidationStrategy = &SpecialTicketFieldsValidationStrategy{}
	default:
		slog.FromContext(context.Background()).Info("SetCountryStrategy", "using default validation strategy for ticket request validation")
		ticketFieldsValidationStrategy = &DefaultFieldsValidationStrategy{}
	}

	ticketValidationContext = NewTicketValidationContext(ticketFieldsValidationStrategy)
}

// GetTicketValidationContext returns the singleton validation context
func GetTicketValidationContext() *TicketValidationContext {
	if ticketValidationContext == nil {
		ticketValidationContext = NewTicketValidationContext(&DefaultFieldsValidationStrategy{})
	}
	return ticketValidationContext
}

// TicketValidationContext manages the validation strategy
type TicketValidationContext struct {
	strategy TicketFieldsValidationStrategy
}

func NewTicketValidationContext(strategy TicketFieldsValidationStrategy) *TicketValidationContext {
	return &TicketValidationContext{
		strategy: strategy,
	}
}

func (tvc *TicketValidationContext) Validate(ctx context.Context, req *api.CreateTicketRequest) error {
	return tvc.strategy.Validate(ctx, req)
}

// ValidateTicketRequest validates a ticket request using the singleton strategy
func ValidateTicketRequest(ctx context.Context, req *api.CreateTicketRequest) error {
	context := GetTicketValidationContext()

	slog.FromContext(ctx).Info("ValidateTicketRequest", "validating request using singleton strategy")

	return context.Validate(ctx, req)
}

{"name": "Onedash BE Service", "serviceName": "onedash-api", "env": "dev", "host": "0.0.0.0", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "root:@tcp(127.0.0.1:30001)/onedash?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "slave": {"dsn": "root:@tcp(127.0.0.1:30001)/onedash?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126, "disable": true}, "redisConfig": {"addr": "localhost:30002", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "tlsEnabled": false}, "redisAppConfig": {"expiryTimeForRedis": 3600}, "logger": {"syslogTag": "structuredlog.onedash", "workerCount": 3, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": true}, "customerPortalConfig": {"serviceName": "customer-portal", "hostAddress": "https://backend-apps.dev.idbank-int.net/customer-portal", "circuitConfig": {"customerportal": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}}, "auditTrailQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ce-log-audit-trail-sqs20240904073103891900000004", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 3, "tag": "sqs.AuditTrails", "enabled": false}, "unlinkAccountQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ops-unlink-account-sqs20240911042940531100000006", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 1, "tag": "sqs.<PERSON><PERSON><PERSON><PERSON>unt", "enabled": false}, "transferOnBehalfQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ops-transfer-on-behalf-sqs20240911042940527500000004", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 1, "tag": "sqs.TransferOnBehalf", "enabled": false}, "unblockAccountQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ops-unblock-account-sqs20240904073103891900000005", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 1, "tag": "sqs.<PERSON><PERSON><PERSON><PERSON>unt", "enabled": false}, "blockAccountQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ops-block-account-sqs20240904073103893100000006", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 1, "tag": "sqs.BlockAccount", "enabled": false}, "updateAccountStatusQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ops-update-account-status-sqs20240930055735912900000002", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 1, "tag": "sqs.UpdateAccountStatus", "enabled": false}, "deactivateLOCQueue": {"queueURL": "https://sqs.ap-southeast-3.amazonaws.com/************/dev-backend-onedash-api-ops-deactivate-loc-sqs20240925082706855500000002", "awsRegion": "ap-southeast-3", "delaySeconds": 1, "waitTimeSeconds": 20, "noWorker": 1, "tag": "sqs.DeactivateLOC", "enabled": false}, "logAuditTrailsRules": {"CRMRule": {"timeRange": 5}}, "accountServiceConfig": {"serviceName": "account-service", "baseURL": "https://backend-apps.dev.idbank-int.net/account-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "payAuthZConfig": {"serviceName": "pay-authz", "baseURL": "https://backend-apps.dev.idbank-int.net/pay-authz", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "paymentOpsTrfConfig": {"serviceName": "payment-ops-trf", "baseURL": "https://backend-apps.dev.idbank-int.net/payment-ops-trf", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "hedwig": {"clientConfig": {"hostAddress": "https://backend-apps.dev.idbank-int.net/hedwig", "pushInboxServerPath": "/hedwig/v1/pushInbox", "pushServerPath": "/hedwig/v1/push", "emailServerPath": "/hedwig/v1/email", "circuitConfig": {"hedwig": {"timeoutInMs": 15}}, "serviceName": "", "serviceKey": ""}, "pushNotificationTemplateMappings": {"push_block_account": "685a8a5b-6691-4a4c-ab34-174cdd273211", "push_unblock_account": "a728abef-526d-45cc-baea-1a012d14f2cf"}, "emailNotificationTemplateMappings": {"email_block_account": "fd49d3fa-1b4e-4e5e-a177-d6fb42cf8843"}, "pushInboxNotificationTemplateMappings": {"push_inbox_block_account": "399a1798-5e04-465e-a0ac-a89d7c9b203e", "push_inbox_unblock_account": "58c5c0b4-725d-4d54-b388-8d47eeadd561"}}, "loanCoreLocKafkaConfig": {"brokers": ["b-1.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094", "b-2.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094", "b-3.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094"], "clientID": "ops-onedash-client", "clusterType": "critical", "enableTLS": true, "topicName": "dev-loan-core-loc-account", "packageName": "pb", "dtoName": "LoanCoreLoc", "initOffset": "oldest"}, "depositsAccountDetailEventKafkaConfig": {"brokers": ["b-1.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094", "b-2.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094", "b-3.idbankdevbackendm.7iab3y.c3.kafka.ap-southeast-3.amazonaws.com:9094"], "clientID": "ops-onedash-client", "clusterType": "critical", "enableTLS": true, "stream": "dev-deposits-account-detail-event", "packageName": "pb", "dtoName": "AccountDetail", "offsetType": "oldest"}, "lendingActionStatusKafkaConfig": {"brokers": [], "clientID": "ops-onedash-client", "clusterType": "critical", "enableTLS": false, "topicName": "dev-ops-lending-action-status-event", "packageName": "pb", "dtoName": "OpsLendingActionStatusEvent", "initOffset": "oldest", "enable": false}, "lendingActionKafkaConfig": {"brokers": [], "clientID": "ops-onedash-client", "clusterType": "critical", "enableTLS": false, "topicName": "dev-ops-lending-action-event", "packageName": "pb", "dtoName": "OpsLendingActionEvent", "initOffset": "oldest", "enable": false}, "tokenKey": {"accessTokenKey": "secret-key", "refreshTokenKey": "refresh-key", "accessTokenExpiryTime": 30, "refreshTokenExpiryTime": 720}, "jumpcloud": {"baseURL": "https://oauth.id.jumpcloud.com/userinfo"}, "s3": {"bucketName": "idbank-dev-backend-onedash-api", "bucketUrl": "https://idbank-dev-backend-onedash-api.s3.ap-southeast-3.amazonaws.com", "directory": "onedash-documents", "allowedMimeTypes": ["image/png", "image/jpeg", "image/jpg", "application/pdf", "text/csv", "application/zip", "application/x-zip-compressed"], "maxFileSize": ********}, "customerMasterConfig": {"serviceName": "customer-master", "baseURL": "https://backend-apps.dev.idbank-int.net/customer-master", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "customerExperienceConfig": {"serviceName": "customer-experience", "baseURL": "https://backend-apps.dev.idbank-int.net/customer-experience", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "chatbotServiceConfig": {"serviceName": "chatbot-service", "hostAddress": "https://api-chatbot.dev.super-id.net", "circuitBreaker": {"timeout": 10000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "scheduler": {"writeOffJob": {"cronExpression": "* * * * *", "schedulerLockDurationInSec": 30}, "waiveOffJob": {"cronExpression": "* * * * *", "schedulerLockDurationInSec": 30}}, "fileProcessor": {"writeOffFileProcessorConfig": {"maxRecords": 500, "timeout": 600, "recordTTL": 86400}, "waiveOffFileProcessorConfig": {"maxRecords": 500, "timeout": 600, "recordTTL": 86400}}, "appianConfig": {"hostAddress": "https://superops-dev.super-id.net", "clientID": "appian.api.onedash", "circuitConfig": {"appian": {"timeout": 15}}, "registeredClientID": "SeqQd_va2fHf_hFHvpPzw4lAzz5u3ytVI058G-2HhWI", "registeredClientSecret": "kLyShy9guu3v5xvymQ2q9jkVRI-3lVJk1D2AvUYoJdZyd2t_BXb4wqfBycv5JX5Toi0FhLi9nhjMfG3hH03ggDhaJDp8b40TYGkJiNRVij17o-ZKbv-zg-l6L6wqna6BCa2MajnYovms6IhLu5hgbTyTrThij3xu1tjkQQBVsjo1ZZ2teMrXQCeu10ojQOw0AWMAVMSyDa6E1BWJhefl1URZMBxUGTm7tdkMkrSJ7qVsvY7yZH3_DNLp6e0an02DFPp-rbnpDnfTMltOmCKxs922coU9t34vajGTD-jH7uCDYI3vd9is4AuSYhXAo_kMY4_SBdtcMnamKs6GWneprA==", "grantType": "client_credentials"}, "productMasterConfig": {"serviceName": "product-master", "baseURL": "https://backend-apps.dev.idbank-int.net/product-master", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "transactionHistoryConfig": {"serviceName": "transaction-history", "baseURL": "https://backend-apps.dev.idbank-int.net/transaction-history", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "transactionLimitConfig": {"serviceName": "transaction-limit", "baseURL": "https://backend-apps.dev.idbank-int.net/transaction-limit", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "customerJournalConfig": {"serviceName": "customer-journal", "baseURL": "https://backend-apps.dev.idbank-int.net/customer-journal", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "customerJourneyExperienceConfig": {"serviceName": "customer-journey-experience", "baseURL": "https://backend-apps.dev.idbank-int.net/customer-journey-experience", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "amlServiceConfig": {"serviceName": "aml-service", "baseURL": "https://backend-apps.dev.idbank-int.net/aml-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "paymentServiceConfig": {"partnerID": "6a60b540-d245-48e3-88e1-e14d7451ccdf"}, "grabIdConfig": {"clientConfig": {"hostAddress": "https://backend-apps.dev.idbank-int.net/grab-id", "getAuthStatus": "/v1/oauth2/asyncauth/status", "getUserStatus": "/v1/internal/user/status", "circuitConfig": {"inAppAuth": {"timeoutInMs": 15}}, "serviceKey": "{{ grabid_api_key }}"}}, "locale": {"homeCountry": "MY"}, "featureflag": {"useConcurrentLoginLimit": false, "useNativeLogin": true, "skipValidationForDraftTicket": true}, "servicesCredential": {"amlService.onedash.svc": {"clientID": "amlService.onedash.svc", "clientSecret": "asdfgh"}}, "loanExpConfig": {"baseURL": "https://backend-apps.dev.idbank-int.net/loan-exp", "serviceName": "loan-exp", "isHealthCheckDisabled": false, "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403]}}, "loanCoreConfig": {"isHealthCheckDisabled": false, "serviceName": "loan-core", "baseURL": "https://backend-apps.dev.idbank-int.net/loan-core", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "loanAppConfig": {"isHealthCheckDisabled": false, "serviceName": "loan-app", "baseURL": "https://backend-apps.dev.idbank-int.net/loan-app", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "ops": {"serviceName": "ops-service", "baseURL": "http://localhost:8080", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}}
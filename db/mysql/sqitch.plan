%syntax-version=1.0.0
%project=onedash

001-onedash 2024-09-03T07:04:37Z vinsensius.suryanto <<EMAIL>> # setup db
002-log-audit-trail 2024-09-07T00:57:26Z vinsensius.suryanto <<EMAIL>> # add schema
003-queue 2024-09-11T07:36:48Z vinsensius.suryanto <<EMAIL>> # add schema
004-log-audit-trails 2024-09-23T03:25:37Z vinsensius.suryanto <<EMAIL>> # create index for log audit trails
005-log-audit-trail.sql 2024-10-03T06:06:01Z vinsensius.suryanto <<EMAIL>> # modify column size
006-add-queue-identifier-column 2024-10-04T03:25:37Z shannia.melianti <<EMAIL>> # add column identifier for queue
007-log-audit-trails 2024-10-15T03:25:37Z vinsensius.suryanto <<EMAIL>> # add field safe ID
008-tickets 2024-10-17T06:07:45Z vinsensius.suryanto <<EMAIL>> # add ticket flow
009-users 2024-10-21T10:25:37Z cindy.sari <<EMAIL>> # remove is temporary and add user id field
010-applications-roles 2024-10-21T10:57:00Z shannia.melianti <<EMAIL>> # add application table and add new column
013-tickets 2024-11-14T06:38:37Z vinsensius.suryanto <<EMAIL>> # add ticket supporting schema
014-tickets-assignee 2024-11-22T06:41:16Z vinsensius.suryanto <<EMAIL>> # add assignee by user id
011-user-roles-permission-status 2024-11-25T12:36:37Z cindy.sari <<EMAIL>> # add user roles permission status
012-add-constraint-unique 2024-11-25T12:36:37Z cindy.sari <<EMAIL>> # add constraint unique
015-feature-flag 2024-11-25T12:36:37Z cindy.sari <<EMAIL>> # add feature flag
016-tickets-assignee-idx 2024-12-02T08:33:24Z vinsensius.suryanto <<EMAIL>> # add assignee index
017-permissions 2024-12-04T07:36:37Z shannia.melianti <<EMAIL>> # add permission status
018-documents 2024-10-17T09:43:51Z vinsensius.suryanto <<EMAIL>> # add documents
019-element-module-status 2024-12-19T08:14:07Z vinsensius.suryanto <<EMAIL>> # add status on element & modules
020-elements-has-ticket 2025-01-03T10:54:09Z vinsensius.suryanto <<EMAIL>> # add has ticket on element
021-segregations 2024-11-25T12:36:37Z cindy.sari <<EMAIL>> # add segregations table
022-default-application-id 2024-11-25T12:36:37Z shannia.melianti <<EMAIL>> # set default application id on modules
023-bulk-processor 2025-05-24T06:55:57Z vinsensius.suryanto <<EMAIL>> # add bulk processor dumps
024-documents-type-desc 2025-06-16T08:37:47Z vinsensius.suryanto <<EMAIL>> # add docs type and description
025-documents-count-parent-document-id-validation-status 2025-06-25T09:41:15Z v.fajar.alfiantino <<EMAIL>> # Add count, parent_document_id, and validation_status to documents table
025-customer-segment 2025-06-10T08:12:30Z leanlynn.oh <<EMAIL>> # add customer segment schema
026-ticket-requestor 2025-06-10T08:23:06Z leanlynn.oh <<EMAIL>># add ticket requestor schema
027-tickets 2025-06-10T15:34:32Z leanlynn.oh <<EMAIL>> # add ticket category, extra ticket details on tickets
028-child-ticket 2025-07-08T04:51:20Z vinsensius.suryanto <<EMAIL>> # add column to support child ticket
029-add-calendar-tables 2025-07-10T12:00:00Z system <<EMAIL>> # add calendar configuration tables for SLA calculations
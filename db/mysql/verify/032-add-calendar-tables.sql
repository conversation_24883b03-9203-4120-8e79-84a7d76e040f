-- Verify onedash:032-add-calendar-tables.sql on mysql

BEGIN;

-- Verify calendars table exists and has correct structure
SELECT id, name, description, type, is_active, created_at, updated_at, created_by, updated_by
FROM calendars
WHERE FALSE;

-- Verify calendar_working_hours table exists and has correct structure
SELECT id, calendar_id, day_of_week, start_time, end_time, created_at, updated_at, created_by, updated_by
FROM calendar_working_hours
WHERE FALSE;

-- Verify calendar_holidays table exists and has correct structure
SELECT id, calendar_id, holiday_date, name, description, created_at, updated_at, created_by, updated_by
FROM calendar_holidays
WHERE FALSE;

-- Verify indexes exist
SHOW INDEX FROM calendars WHERE Key_name = 'idx_calendars_type';
SHOW INDEX FROM calendars WHERE Key_name = 'idx_calendars_active';
SHOW INDEX FROM calendar_working_hours WHERE Key_name = 'idx_working_hours_calendar';
SHOW INDEX FROM calendar_holidays WHERE Key_name = 'idx_holidays_calendar';
SHOW INDEX FROM calendar_holidays WHERE Key_name = 'idx_holidays_date';
SHOW INDEX FROM element_calendars WHERE Key_name = 'idx_element_calendars_element';

ROLLBACK;

-- Deploy onedash:013-feature-flag to mysql
BEGIN;

CREATE TABLE `feature_flag` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
     `created_by` bigint NOT NULL,
     `updated_at` timestamp NULL DEFAULT NULL,
     `updated_by` bigint DEFAULT NULL,
     `name` varchar(255) NOT NULL,
     `value` tinyint NOT NULL,
     `description` varchar(255) DEFAULT NULL,
     `status` tinyint NOT NULL,
     PRIMARY KEY (`id`),
     CONSTRAINT feature_flag_unique_key UNIQUE(`name`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

COMMIT;

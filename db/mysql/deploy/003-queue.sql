-- Deploy onedash:003-queue to mysql

BEGIN;

CREATE TABLE `queue` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` bigint NOT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  `identifier_type` varchar(255) DEFAULT NULL,
  `status` int DEFAULT NULL,
  `event_name` varchar(255) DEFAULT NULL,
  `source` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

COMMIT;

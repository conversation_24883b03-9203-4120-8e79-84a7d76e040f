-- Deploy onedash:026-ticket-indexes to mysql

BEGIN;

-- Single-Column Indexes for tickets table
ALTER TABLE `tickets` ADD INDEX t_priority_id_idx (priority_id);
ALTER TABLE `tickets` ADD INDEX t_ticket_status_id_idx (ticket_status_id);
ALTER TABLE `tickets` ADD INDEX t_created_at_idx (created_at);
ALTER TABLE `tickets` ADD INDEX t_domain_id_idx (domain_id);
ALTER TABLE `tickets` ADD INDEX t_customer_segment_id_idx (customer_segment_id);

# -- Composite Indexes for tickets table
ALTER TABLE `tickets` ADD INDEX t_created_at_id_idx (created_at, id);
ALTER TABLE `tickets` ADD INDEX t_element_id_created_at_idx (element_id, created_at);
ALTER TABLE `tickets` ADD INDEX t_ticket_status_id_created_at_idx (ticket_status_id, created_at);
ALTER TABLE `tickets` ADD INDEX t_case_category_subcategory_idx (case_category, case_subcategory);
<PERSON>TER TABLE `tickets` ADD INDEX t_case_category_created_at_idx (case_category, created_at);
ALTER TABLE `tickets` ADD INDEX t_ticket_status_id_close_datetime_idx (ticket_status_id, ticket_close_datetime);

# -- Single-Column Indexes for elements table
ALTER TABLE `elements` ADD INDEX e_name_idx (name);

-- Single-Column Indexes for customer_segment table
ALTER TABLE `customer_segment` ADD INDEX cs_name_idx (name);

-- Single-Column Indexes for users table
ALTER TABLE `users` ADD INDEX u_name_idx (name);

COMMIT;
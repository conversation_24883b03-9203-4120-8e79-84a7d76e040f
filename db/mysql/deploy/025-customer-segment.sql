-- Deploy onedash:023-customer-segment to mysql

BEGIN;

CREATE TABLE IF NOT EXISTS `customer_segment` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `created_by` bigint NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_by` bigint DEFAULT NULL,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `name` varchar(255) NOT NULL,
    PRIMARY KEY (`id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

COMMIT;

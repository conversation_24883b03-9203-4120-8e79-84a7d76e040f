-- Deploy onedash:032-add-calendar-tables.sql to mysql

BEGIN;

-- Calendar master table
CREATE TABLE calendars (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- 'GXS', 'GXB', 'GXB_CE', etc.
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT
);

-- Working hours configuration
CREATE TABLE calendar_working_hours (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    calendar_id BIGINT NOT NULL,
    day_of_week TINYINT NOT NULL, -- 0=Sunday, 1=Monday, etc.
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    UNIQUE KEY unique_calendar_day (calendar_id, day_of_week)
);

-- Public holidays
CREATE TABLE calendar_holidays (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    calendar_id BIGINT NOT NULL,
    holiday_date DATE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    UNIQUE KEY unique_calendar_holiday (calendar_id, holiday_date)
);

-- Element to calendar mapping (for multi-calendar support)
CREATE TABLE element_calendars (
   element_id BIGINT NOT NULL,
   calendar_id BIGINT NOT NULL,
   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   created_by BIGINT,
   updated_by BIGINT,
   PRIMARY KEY (element_id, calendar_id)
);


-- Add indexes for performance
CREATE INDEX idx_calendars_type ON calendars(type);
CREATE INDEX idx_calendars_active ON calendars(is_active);
CREATE INDEX idx_working_hours_calendar ON calendar_working_hours(calendar_id);
CREATE INDEX idx_holidays_calendar ON calendar_holidays(calendar_id);
CREATE INDEX idx_holidays_date ON calendar_holidays(holiday_date);
CREATE INDEX idx_element_calendars_element ON element_calendars(element_id);

COMMIT;
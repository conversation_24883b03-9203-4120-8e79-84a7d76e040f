package storage

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

// DataJSON ...
type DataJSON map[string]any

// Value ...
func (a DataJSON) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// Scan ...
func (a *DataJSON) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid (%v)", value)
	}

	return json.Unmarshal(b, &a)
}

// CaseDTO represents the case data transfer object
type CaseDTO struct {
	ID           int64                  `json:"id" db:"id"`
	CaseTypeID   int64                  `json:"case_type_id" db:"case_type_id"`
	PriorityID   string                 `json:"priority_id" db:"priority_id"`
	DeadlineTime sql.NullString         `json:"deadline_time" db:"deadline_time"`
	CaseStatusID int64                  `json:"case_status_id" db:"case_status_id"`
	Data         map[string]interface{} `json:"data" db:"data"`
	CreatedAt    sql.NullString         `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullString         `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullInt64          `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullInt64          `json:"updated_by" db:"updated_by"`
}

// CaseHistoryDTO represents the case history data transfer object
type CaseHistoryDTO struct {
	ID         int64                  `json:"id" db:"id"`
	CaseID     int64                  `json:"case_id" db:"case_id"`
	ActionName string                 `json:"action_name" db:"action_name"`
	Data       map[string]interface{} `json:"data" db:"data"`
	Note       map[string]interface{} `json:"note" db:"note"`
	CreatedAt  sql.NullString         `json:"created_at" db:"created_at"`
	CreatedBy  sql.NullInt64          `json:"created_by" db:"created_by"`
}

// --- Onedash DTOs ---

// ModuleDTO represents the module data transfer object
type ModuleDTO struct {
	ID        int64         `json:"id" db:"id"`
	CreatedAt sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name      string        `json:"name" db:"name"`
	Status    int32         `json:"status" db:"status"`
}

// ModuleListDTO represents the module data transfer object
type ModuleListDTO struct {
	ID           int64          `json:"id" db:"id"`
	CreatedAt    sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy    sql.NullString `json:"created_by" db:"created_by"`
	UpdatedBy    sql.NullString `json:"updated_by" db:"updated_by"`
	Name         string         `json:"name" db:"name"`
	Status       int32          `json:"status" db:"status"`
	HasTicketing bool           `json:"has_ticketing" db:"has_ticketing"`
}

// ElementDTO represents the role permission data transfer object
type ElementDTO struct {
	ID                       int64         `json:"id" db:"id"`
	CreatedAt                sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt                sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy                sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy                sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name                     string        `json:"name" db:"name"`
	ModuleID                 int64         `json:"module_id" db:"module_id"`
	DefaultPriorityID        int64         `json:"default_priority_id" db:"default_priority_id"`
	Code                     string        `json:"code" db:"code"`
	Status                   int32         `json:"status" db:"status"`
	HasTicketing             bool          `json:"has_ticketing" db:"has_ticketing"`
	DefaultCustomerSegmentID sql.NullInt64 `json:"default_customer_segment_id" db:"default_customer_segment_id"`
	DefaultTicketRequestorID sql.NullInt64 `json:"default_ticket_requestor_id" db:"default_ticket_requestor_id"`
}

// ElementListDTO represents the element list data transfer object, the creator/updater is being returned as string
type ElementListDTO struct {
	ID                       int64          `json:"id" db:"id"`
	Name                     string         `json:"name" db:"name"`
	Code                     string         `json:"code" db:"code"`
	ModuleID                 int64          `json:"module_id" db:"module_id"`
	DefaultPriorityID        int64          `json:"default_priority_id" db:"default_priority_id"`
	CreatedAt                sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt                sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy                sql.NullString `json:"created_by" db:"created_by"`
	UpdatedBy                sql.NullString `json:"updated_by" db:"updated_by"`
	Status                   int32          `json:"status" db:"status"`
	HasTicketing             bool           `json:"has_ticketing" db:"has_ticketing"`
	DefaultCustomerSegmentID sql.NullInt64  `json:"default_customer_segment_id" db:"default_customer_segment_id"`
	DefaultTicketRequestorID sql.NullInt64  `json:"default_ticket_requestor_id" db:"default_ticket_requestor_id"`
}

// TicketStatusDTO represents the ticket status data transfer object
type TicketStatusDTO struct {
	ID        int64         `json:"id" db:"id"`
	CreatedAt sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name      string        `json:"name" db:"name"`
}

// TicketChainDTO represents the ticket chain data transfer object
type TicketChainDTO struct {
	ID              int64          `json:"id" db:"id"`
	CreatedAt       sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt       sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy       sql.NullInt64  `json:"created_by" db:"created_by"`
	UpdatedBy       sql.NullInt64  `json:"updated_by" db:"updated_by"`
	CurrentStatusID int64          `json:"current_status_id" db:"current_status_id"`
	NextStatusID    int64          `json:"next_status_id" db:"next_status_id"`
	ElementID       int64          `json:"element_id" db:"element_id"`
	ActionName      string         `json:"action_name" db:"action_name"`
	BitwiseRequired int64          `json:"bitwise_required" db:"bitwise_required"`
	Conditions      sql.NullString `json:"conditions" db:"conditions"`
}

// TicketPriorityDTO represents the ticket priority data transfer object
type TicketPriorityDTO struct {
	ID               int64         `json:"id" db:"id"`
	CreatedAt        sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt        sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy        sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy        sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name             string        `json:"name" db:"name"`
	TimeToResolveSec int64         `json:"time_to_resolve_sec" db:"time_to_resolve_sec"`
	ElementID        int64         `json:"element_id" db:"element_id"`
}

// TicketDTO represents the ticket data transfer object
type TicketDTO struct {
	ID                   int64           `json:"id" db:"id"`
	CreatedAt            sql.NullTime    `json:"created_at" db:"created_at"`
	UpdatedAt            sql.NullTime    `json:"updated_at" db:"updated_at"`
	CreatedBy            sql.NullInt64   `json:"created_by" db:"created_by"`
	UpdatedBy            sql.NullInt64   `json:"updated_by" db:"updated_by"`
	ElementID            int64           `json:"element_id" db:"element_id"`
	ElementName          string          `json:"element_name" db:"element_name"`
	PriorityID           int64           `json:"priority_id" db:"priority_id"`
	PriorityName         string          `json:"priority_name" db:"priority_name"`
	DeadlineTime         sql.NullTime    `json:"deadline_time" db:"deadline_time"`
	TicketStatusID       int64           `json:"ticket_status_id" db:"ticket_status_id"`
	Data                 *api.TicketData `json:"data" db:"data"`
	Source               string          `json:"source" db:"source"`
	AssigneeUserID       sql.NullInt64   `json:"assignee_user_id" db:"assignee_user_id"`
	AssigneeUserName     sql.NullString  `json:"assignee_user_name" db:"assignee_user_name"`
	CaseCategory         sql.NullString  `json:"case_category" db:"case_category"`
	CaseSubcategory      sql.NullString  `json:"case_subcategory" db:"case_subcategory"`
	CustomerSegmentID    sql.NullInt64   `json:"customer_segment_id" db:"customer_segment_id"`
	CustomerSegmentName  sql.NullString  `json:"customer_segment_name" db:"customer_segment_name"`
	DomainID             sql.NullString  `json:"domain_id" db:"domain_id"`
	TicketRequestorID    sql.NullInt64   `json:"ticket_requestor_id" db:"ticket_requestor_id"`
	TicketRequestorName  sql.NullString  `json:"ticket_requestor_name" db:"ticket_requestor_name"`
	Channel              sql.NullString  `json:"channel" db:"channel"`
	TicketCloseDatetime  sql.NullTime    `json:"ticket_close_datetime" db:"ticket_close_datetime"`
	ParentTicketID       sql.NullInt64   `json:"parent_ticket_id" db:"parent_ticket_id"`
	StatusName           string          `json:"status_name" db:"status_name"`
	ModuleName           string          `json:"module_name" db:"module_name"`
	HoldReason           sql.NullString  `json:"hold_reason" db:"hold_reason"`
	HoldRemarks          sql.NullString  `json:"hold_remarks" db:"hold_remarks"`
	OnHoldAt             sql.NullTime    `json:"on_hold_at" db:"on_hold_at"`
	ResumedAt            sql.NullTime    `json:"resumed_at" db:"resumed_at"`
	TotalHoldDurationSec sql.NullInt64   `json:"total_hold_duration_sec" db:"total_hold_duration_sec"`
	OriginalDeadlineTime sql.NullTime    `json:"original_deadline_time" db:"original_deadline_time"`
}

// TicketApprovalHistory represents the ticket approval history data transfer object
type TicketApprovalHistory struct {
	UserID    int64  `json:"userID"`
	UserName  int64  `json:"userName"`
	Action    string `json:"action"`
	CreatedAt string `json:"createdAt"`
	Comment   string `json:"comment"`
}

// TicketHistoryDTO represents the ticket history data transfer object
type TicketHistoryDTO struct {
	ID            int64           `json:"id" db:"id"`
	CreatedAt     sql.NullTime    `json:"created_at" db:"created_at"`
	CreatedBy     sql.NullInt64   `json:"created_by" db:"created_by"`
	CreatedByName sql.NullString  `json:"created_by" db:"created_by"`
	UpdatedAt     sql.NullTime    `json:"updated_at" db:"updated_at"`
	UpdatedBy     sql.NullInt64   `json:"updated_by" db:"updated_by"`
	TicketID      int64           `json:"ticket_id" db:"ticket_id"`
	Data          *api.TicketData `json:"data" db:"data"`
	ActionName    string          `json:"action_name" db:"action_name"`
	Note          sql.NullString  `json:"note" db:"note"`
	PrevStatusID  int64           `json:"prev_status_id" db:"prev_status_id"`
	NextStatusID  int64           `json:"next_status_id" db:"next_status_id"`
}

// AuditTrailsDTO represents the audit trails log data transfer object on onedash
type AuditTrailsDTO struct {
	ID             int64         `json:"id" db:"id"`
	CreatedAt      sql.NullTime  `json:"created_at" db:"created_at"`
	CreatedBy      sql.NullInt64 `json:"created_by" db:"created_by"`
	Identifier     string        `json:"identifier" db:"identifier"`
	IdentifierType string        `json:"identifier_type" db:"identifier_type"`
	Title          string        `json:"title" db:"title"`
	Description    string        `json:"description" db:"description"`
	ActivityType   string        `json:"activity_type" db:"activity_type"`
	ReferenceID    string        `json:"reference_id" db:"reference_id"`
	UserName       string        `json:"user_name" db:"user_name"`
	ExtraParams    *DataJSON     `json:"extra_params" db:"extra_params"`
}

// GetAuditTrailsRequestWithPaginationDto dto that needed to make sure req is not null 'withPagination=true'
type GetAuditTrailsRequestWithPaginationDto struct {
	Limit          int64    `json:"limit,omitempty"`
	StartingBefore string   `json:"startingBefore,omitempty"`
	EndingAfter    string   `json:"endingAfter,omitempty"`
	ActivityType   string   `json:"activityType,omitempty"`
	SortBy         api.Sort `json:"sortBy,omitempty" validate:"omitempty,audit_trails_sort"`
}

// CustomerLogDTO represents the customer log data transfer object
type CustomerLogDTO struct {
	ID              int64         `json:"id" db:"id"`
	CreatedAt       sql.NullTime  `json:"created_at" db:"created_at"`
	CreatedBy       sql.NullInt64 `json:"created_by" db:"created_by"`
	IdentifierValue string        `json:"identifier_value" db:"identifier_value"`
	IdentifierType  string        `json:"identifier_type" db:"identifier_type"`
	TicketID        int64         `json:"ticket_id" db:"ticket_id"`
	CustomerID      string        `json:"customer_id" db:"customer_id"`
}

// --- Helper DTOs ---

// NextTicketAction represents the next ticket action data transfer object
type NextTicketAction struct {
	NextStatusID     int64  `json:"next_status_id"`
	CanUpdatePayload bool   `json:"can_update_payload"`
	ActionName       string `json:"action_name"`
}

// LogAuditTrail ...
type LogAuditTrail struct {
	ID        uint64      `sql-col:"id" sql-key:"id" sql-insert:"false" db:"id"`
	Name      string      `sql-col:"name" db:"name"`
	UserID    string      `sql-col:"user_id" db:"user_id"`
	Email     string      `sql-col:"email" db:"email"`
	Event     string      `sql-col:"event" db:"event"`
	Action    string      `sql-col:"action" db:"action"`
	Metadata  interface{} `sql-col:"metadata" data-type:"json" db:"metadata"`
	RelatedID string      `sql-col:"related_id" db:"related_id"`
	Service   string      `sql-col:"service" db:"service"`
	CreatedAt time.Time   `sql-col:"created_at" db:"created_at"`
	SafeID    string      `sql-col:"safe_id" db:"safe_id"`
}

// QueueDTO ...
type QueueDTO struct {
	ID             int64                  `json:"id" db:"id"`
	CreatedAt      sql.NullTime           `json:"created_at" db:"created_at"`
	CreatedBy      sql.NullInt64          `json:"created_by" db:"created_by"`
	UpdatedAt      sql.NullTime           `json:"updated_at" db:"updated_at"`
	UpdatedBy      sql.NullInt64          `json:"updated_by" db:"updated_by"`
	IdentifierType string                 `json:"identifier_type" db:"identifier_type"`
	Identifier     string                 `json:"identifier" db:"identifier"`
	TicketID       string                 `json:"ticket_id" db:"ticket_id"`
	Status         int32                  `json:"status" db:"status"`
	EventName      string                 `json:"event_name" db:"event_name"`
	Source         string                 `json:"source" db:"source"`
	Metadata       map[string]interface{} `json:"metadata" db:"metadata"`
}

// ModuleElementPermissionsDTO is for fetching module element permission in get role detail
type ModuleElementPermissionsDTO struct {
	ElementID    int64  `json:"element_id" db:"element_id"`
	ElementName  string `json:"element_name" db:"element_name"`
	ModuleID     int64  `json:"module_id" db:"module_id"`
	ModuleName   string `json:"module_name" db:"module_name"`
	BitwiseValue int64  `json:"bitwise_value" db:"bitwise_value"`
}

// FeatureFlagDTO ...
type FeatureFlagDTO struct {
	ID          int64         `json:"id" db:"id"`
	CreatedAt   sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedAt   sql.NullTime  `json:"updated_at" db:"updated_at"`
	CreatedBy   sql.NullInt64 `json:"created_by" db:"created_by"`
	UpdatedBy   sql.NullInt64 `json:"updated_by" db:"updated_by"`
	Name        string        `json:"name" db:"name"`
	Value       int32         `json:"value" db:"value"`
	Description string        `json:"description" db:"description"`
	Status      int32         `json:"status" db:"status"`
}

// FeatureFlagListDTO ...
type FeatureFlagListDTO struct {
	CreatedAt   sql.NullTime   `json:"created_at" db:"created_at"`
	UpdatedAt   sql.NullTime   `json:"updated_at" db:"updated_at"`
	CreatedBy   sql.NullString `json:"created_by" db:"created_by"`
	UpdatedBy   sql.NullString `json:"updated_by" db:"updated_by"`
	Name        string         `json:"name" db:"name"`
	Value       int32          `json:"value" db:"value"`
	Description string         `json:"description" db:"description"`
	Status      int32          `json:"status" db:"status"`
}

// DocumentDTO represents the document data transfer object
type DocumentDTO struct {
	ID               int64          `json:"id" db:"id"`
	CreatedAt        sql.NullTime   `json:"created_at" db:"created_at"`
	CreatedBy        sql.NullInt64  `json:"created_by" db:"created_by"`
	UpdatedAt        sql.NullTime   `json:"updated_at" db:"updated_at"`
	UpdatedBy        sql.NullInt64  `json:"updated_by" db:"updated_by"`
	Name             string         `json:"name" db:"name"`
	URL              string         `json:"url" db:"url"`
	TicketID         sql.NullInt64  `json:"ticket_id" db:"ticket_id"`
	Status           int            `json:"status" db:"status"`
	Type             sql.NullString `json:"type" db:"type"`
	Description      sql.NullString `json:"description" db:"description"`
	Count            sql.NullInt64  `json:"count" db:"count"`
	ValidationStatus sql.NullString `json:"validation_status" db:"validation_status"`
	ParentDocumentID sql.NullInt64  `json:"parent_document_id" db:"parent_document_id"`
}

type DocumentListDTO struct {
	ID          int64          `json:"id" db:"id"`
	CreatedAt   sql.NullTime   `json:"created_at" db:"created_at"`
	CreatedBy   sql.NullString `json:"created_by" db:"created_by"`
	Name        string         `json:"name" db:"name"`
	URL         string         `json:"url" db:"url"`
	TicketID    sql.NullInt64  `json:"ticket_id" db:"ticket_id"`
	Status      int            `json:"status" db:"status"`
	Type        sql.NullString `json:"type" db:"type"`
	Description sql.NullString `json:"description" db:"description"`
}

// SegregationDTO represents the segregation data transfer object
type SegregationDTO struct {
	ID                  int64         `json:"id" db:"id"`
	Name                string        `json:"name" db:"name"`
	Key                 string        `json:"key" db:"keyword"`
	ParentSegregationID sql.NullInt64 `json:"parent_segregation_id" db:"parent_segregation_id"`
	Order               int32         `json:"order" db:"order"`
	Level               int32         `json:"level" db:"level"`
	Status              string        `json:"status" db:"status"`
	Type                string        `json:"type" db:"type"`
}

// RoleSegregationDTO represents the role segregation data transfer object
type RoleSegregationDTO struct {
	ID            int64 `json:"id" db:"id"`
	RoleID        int64 `json:"role_id" db:"role_id"`
	SegregationID int64 `json:"segregation_id" db:"segregation_id"`
}

// DataSegregationListDTO represents the DTO for get data segregation details
type DataSegregationListDTO struct {
	ID                  int64          `json:"id" db:"id"`
	Name                string         `json:"name" db:"name"`
	ParentSegregationID sql.NullInt64  `json:"parent_segregation_id" db:"parent_segregation_id"`
	ParentName          sql.NullString `json:"parent_name" db:"parent_name"`
	Status              int64          `json:"status" db:"status"`
	HasChild            bool           `json:"has_child" db:"has_child"`
}

// DataSegregationEligibility represents the DTO to check eligibility to update the data
type DataSegregationEligibility struct {
	ID                  int64         `json:"id" db:"id"`
	Name                string        `json:"name" db:"name"`
	ParentSegregationID sql.NullInt64 `json:"parent_segregation_id" db:"parent_segregation_id"`
	EligibleUpdate      bool          `json:"eligible_update" db:"eligible_update"`
}

// CustomerSegmentDTO represents the DTO for customer segment
type CustomerSegmentDTO struct {
	ID        int64         `json:"id" db:"id"`
	IsActive  bool          `json:"is_active" db:"is_active"`
	CreatedBy sql.NullInt64 `json:"created_by" db:"created_by"`
	CreatedAt sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedBy sql.NullInt64 `json:"updated_by" db:"updated_by"`
	UpdatedAt sql.NullTime  `json:"updated_at" db:"updated_at"`
	Name      string        `json:"name" db:"name"`
}

type TicketRequestorDTO struct {
	ID        int64         `json:"id" db:"id"`
	IsActive  bool          `json:"is_active" db:"is_active"`
	CreatedBy sql.NullInt64 `json:"created_by" db:"created_by"`
	CreatedAt sql.NullTime  `json:"created_at" db:"created_at"`
	UpdatedBy sql.NullInt64 `json:"updated_by" db:"updated_by"`
	UpdatedAt sql.NullTime  `json:"updated_at" db:"updated_at"`
	Name      string        `json:"name" db:"name"`
}

// BulkFileProccessorDumpDTO represents the bulk file processor dump data transfer object
type BulkFileProccessorDumpDTO struct {
	ID             int64             `json:"id" db:"id"`
	Identifier     string            `json:"identifier" db:"identifier"`
	IdentifierType string            `json:"identifier_type" db:"identifier_type"`
	Type           string            `json:"type" db:"type"`
	Status         string            `json:"status" db:"status"`
	Data           map[string]string `json:"data" db:"data"`
	CreatedAt      time.Time         `json:"created_at" db:"created_at"`
	UpdatedAt      sql.NullTime      `json:"updated_at" db:"updated_at"`
	ReferenceID    sql.NullString    `json:"reference_id" db:"reference_id"`
	FileName       sql.NullString    `json:"file_name" db:"file_name"`
}

// AuditTrailDTO represents the audit trails log data transfer object on onedash
type AuditTrailDTO struct {
	ID             int64          `json:"id" db:"id"`
	Identifier     string         `json:"identifier" db:"identifier"`
	IdentifierType string         `json:"identifier_type" db:"identifier_type"`
	Title          sql.NullString `json:"title" db:"title"`
	Description    sql.NullString `json:"description" db:"description"`
	ActivityType   string         `json:"activity_type" db:"activity_type"`
	ReferenceID    sql.NullString `json:"reference_id" db:"reference_id"`
	ExtraParams    *DataJSON      `json:"extra_params" db:"extra_params"`
	CreatedBy      int64          `json:"created_by" db:"created_by"`
	CreatedAt      sql.NullTime   `json:"created_at" db:"created_at"`
}

// GetField ...
func (d AuditTrailDTO) GetField(withoutID bool) (res []string) {
	res = []string{"id", "identifier", "identifier_type", "title", "description", "activity_type", "reference_id", "extra_params", "created_by", "created_at"}
	if withoutID {
		res = res[1:]
	}

	return res
}

// QueryInsert ...
func (d AuditTrailDTO) QueryInsert() (res string) {
	fields := d.GetField(true)
	colomn, values := "", ""
	for _, v := range fields {
		colomn = fmt.Sprintf("%s,%s", colomn, v)
		values = fmt.Sprintf("%s,%s", values, "?")
	}

	return fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)", d.TableName(), colomn[1:], values[1:])
}

// TableName ...
func (d AuditTrailDTO) TableName() (res string) {
	return "audit_trails"
}

// TicketFieldOptionDTO represents the ticket field option data transfer object
type TicketFieldOptionDTO struct {
	Value string `json:"value" db:"value"`
	Label string `json:"label" db:"label"`
}

// TicketFieldDTO represents the ticket field data transfer object
type TicketFieldDTO struct {
	FieldId      string                 `json:"field_id" db:"field_id"`
	FieldName    string                 `json:"field_name" db:"field_name"`
	FieldType    string                 `json:"field_type" db:"field_type"`
	IsRequired   bool                   `json:"is_required" db:"is_required"`
	DefaultValue string                 `json:"default_value" db:"default_value"`
	Options      []TicketFieldOptionDTO `json:"options" db:"options"`
}

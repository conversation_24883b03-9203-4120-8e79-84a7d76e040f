package storage

import (
	"context"
	"database/sql"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	calendarLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/calendar/logic"
)

// GetTicketSLAByPriorityID returns the time to resolve a ticket based on the priority ID
func GetTicketSLAByPriorityID(ctx context.Context, db *sql.DB, priorityID int64) (time.Time, error) {
	// Prepare the query
	query := `
		SELECT tp.time_to_resolve_sec, tp.element_id
		FROM ticket_priority tp
		WHERE tp.id = ?
	`

	// Execute the query
	var timeToResolveSec int64
	var elementID int64
	err := db.QueryRowContext(ctx, query, priorityID).Scan(&timeToResolveSec, &elementID)
	if err != nil {
		return time.Time{}, err
	}

	// Get the calendar ID for this element
	calendarID, err := calendarLogic.CalendarProcess.GetCalendarIDForElement(ctx, 1)
	if err != nil {
		// If there's an error getting the calendar, fall back to the simple calculation
		return time.Now().Add(time.Second * time.Duration(timeToResolveSec)), nil
	}

	deadline, err := calendarLogic.CalendarProcess.CalculateDeadline(ctx, calendarID, time.Now(), timeToResolveSec)
	if err != nil {
		return time.Time{}, err
	}

	// TODO: Test properly
	return deadline, nil
}

// GetElementPriorities returns the priorities of an element
//
// nolint: errcheck
func GetElementPriorities(ctx context.Context, db *sql.DB, elementID int64) ([]*TicketPriorityDTO, error) {
	// Prepare the query
	query := `
		SELECT id, name, time_to_resolve_sec, element_id, created_at, created_by, updated_at, updated_by
		FROM ticket_priority
		WHERE element_id = ?
	`

	// Execute the query
	rows, err := db.QueryContext(ctx, query, elementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get element priorities")
	}
	defer rows.Close()

	// Scan the result
	var priorities []*TicketPriorityDTO
	for rows.Next() {
		priority := &TicketPriorityDTO{}
		err := rows.Scan(&priority.ID, &priority.Name, &priority.TimeToResolveSec, &priority.ElementID, &priority.CreatedAt, &priority.CreatedBy, &priority.UpdatedAt, &priority.UpdatedBy)
		if err != nil {
			return nil, err
		}
		priorities = append(priorities, priority)
	}

	return priorities, nil
}

// GetPriorities returns all the priorities
//
// nolint: errcheck
func GetPriorities(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]*TicketPriorityDTO, error) {
	// Prepare the query
	query := `
		SELECT id, name, time_to_resolve_sec, element_id, created_at, created_by, updated_at, updated_by
		FROM ticket_priority
	`

	query, args := storage.BuildQuery(query, conditions...)

	// Execute the query
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get priorities")
	}
	defer rows.Close()

	// Scan the result
	var priorities []*TicketPriorityDTO
	for rows.Next() {
		priority := &TicketPriorityDTO{}
		err := rows.Scan(&priority.ID, &priority.Name, &priority.TimeToResolveSec, &priority.ElementID, &priority.CreatedAt, &priority.CreatedBy, &priority.UpdatedAt, &priority.UpdatedBy)
		if err != nil {
			return nil, err
		}
		priorities = append(priorities, priority)
	}

	return priorities, nil
}

// CreateTicketPriority creates a ticket priority
func CreateTicketPriority(ctx context.Context, db *sql.DB, priority *TicketPriorityDTO) (int64, error) {
	// Prepare the query
	query := `
		INSERT INTO ticket_priority (name, time_to_resolve_sec, element_id, created_at, created_by, updated_at, updated_by)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	// Execute the query
	res, err := db.ExecContext(ctx, query, priority.Name, priority.TimeToResolveSec, priority.ElementID, priority.CreatedAt, priority.CreatedBy, priority.UpdatedAt, priority.UpdatedBy)
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to create ticket priority")
	}

	// Get the last inserted ID
	id, err := res.LastInsertId()
	if err != nil {
		return 0, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get last inserted ID")
	}

	return id, nil
}

// UpdateTicketPriority updates a ticket priority
func UpdateTicketPriority(ctx context.Context, db *sql.DB, priority *TicketPriorityDTO) error {
	// Prepare the query
	query := `
		UPDATE ticket_priority
		SET name = ?, time_to_resolve_sec = ?, element_id = ?, updated_at = ?, updated_by = ?
		WHERE id = ?
	`

	// Execute the query
	_, err := db.ExecContext(ctx, query, priority.Name, priority.TimeToResolveSec, priority.ElementID, priority.UpdatedAt, priority.UpdatedBy, priority.ID)
	if err != nil {
		return errorwrapper.WrapError(err, apiError.InternalServerError, "failed to update ticket priority")
	}

	return nil
}

package storage

import (
	"context"
	"database/sql"
	"encoding/json"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
)

// CreateTicket ...
func CreateTicket(ctx context.Context, tx *sql.Tx, c *TicketDTO) (id int64, err error) {
	var query = `INSERT INTO tickets (element_id, priority_id, deadline_time, ticket_status_id, data, created_at, updated_at, created_by, updated_by, source, assignee_user_id, case_category, case_subcategory, ticket_close_datetime, domain_id, channel, ticket_requestor_id, customer_segment_id, parent_ticket_id)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	// marshall the data
	data, err := json.Marshal(c.Data)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	result, err := tx.ExecContext(ctx, query, c.ElementID, c.PriorityID, c.DeadlineTime, c.TicketStatusID, data, c.CreatedAt, c.UpdatedAt, c.CreatedBy, c.UpdatedBy, c.Source, c.AssigneeUserID, c.CaseCategory, c.CaseSubcategory, c.TicketCloseDatetime, c.DomainID, c.Channel, c.TicketRequestorID, c.CustomerSegmentID, c.ParentTicketID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// UpdateTicket ...
func UpdateTicket(ctx context.Context, tx *sql.Tx, c *TicketDTO) error {
	var query = `
		UPDATE tickets 
		SET data = ?, ticket_status_id = ?, updated_at = ?, updated_by = ?, assignee_user_id = ?
		WHERE id = ?
	`

	// marshall the data
	data, err := json.Marshal(c.Data)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	_, err = tx.ExecContext(ctx, query, data, c.TicketStatusID, c.UpdatedAt, c.UpdatedBy, c.AssigneeUserID, c.ID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// UpdateTicketStatus ...
func UpdateTicketStatus(ctx context.Context, tx *sql.Tx, ticketID int64, statusID int64, userID int64, ticketCloseDatetime sql.NullTime) error {
	var query = `
		UPDATE tickets 
		SET ticket_status_id = ?, updated_at = NOW(), updated_by = ?, ticket_close_datetime = ?
		WHERE id = ?`

	_, err := tx.ExecContext(ctx, query, statusID, userID, ticketCloseDatetime, ticketID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// UpdateTicketAssignee ...
func UpdateTicketAssignee(ctx context.Context, db *sql.DB, ticketID int64, assigneeUserID int64) error {
	if assigneeUserID == 0 {
		// set to null
		var query = `UPDATE tickets SET assignee_user_id = NULL, updated_at = NOW() WHERE id = ?`

		_, err := db.ExecContext(ctx, query, ticketID)
		if err != nil {
			return errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		return nil
	}

	var query = `UPDATE tickets SET assignee_user_id = ?, updated_at = NOW() WHERE id = ?`

	_, err := db.ExecContext(ctx, query, assigneeUserID, ticketID)
	if err != nil {
		return errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return nil
}

// GetTicketByID ...
func GetTicketByID(ctx context.Context, db *sql.DB, id int64) (*TicketDTO, error) {
	var query = `
		SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, t.source, t.assignee_user_id, u.name as assignee_user_name,
		       t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel, t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name, t.parent_ticket_id
		FROM tickets t
		LEFT JOIN users u ON t.assignee_user_id = u.id
		LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
		LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id
		WHERE t.id = ?
	`

	var c TicketDTO
	var data string
	err := db.QueryRowContext(ctx, query, id).Scan(&c.ID, &c.ElementID, &c.PriorityID, &c.DeadlineTime, &c.TicketStatusID, &data, &c.CreatedAt, &c.UpdatedAt, &c.CreatedBy, &c.UpdatedBy, &c.Source, &c.AssigneeUserID, &c.AssigneeUserName,
		&c.CaseCategory, &c.CaseSubcategory, &c.TicketCloseDatetime, &c.DomainID, &c.Channel, &c.TicketRequestorID, &c.TicketRequestorName, &c.CustomerSegmentID, &c.CustomerSegmentName, &c.ParentTicketID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "ticket not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	// unmarshall the data
	err = json.Unmarshal([]byte(data), &c.Data)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return &c, nil
}

// GetTicketList retrieves tickets based on query conditions
//
// nolint: errcheck
func GetTicketList(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) ([]TicketDTO, error) {
	baseQuery := `
		SELECT t.id, t.element_id, t.priority_id, t.deadline_time, t.ticket_status_id, 
		       t.data, t.created_at, t.updated_at, t.created_by, t.updated_by, 
		       t.source, u.name as assignee_user_name, e.name as element_name,
			   p.name as priority_name, t.case_category, t.case_subcategory, t.ticket_close_datetime, t.domain_id, t.channel,
			   t.ticket_requestor_id, tr.name as ticket_requestor_name, t.customer_segment_id, cs.name as customer_segment_name,
			   t.parent_ticket_id, s.name as status_name, m.name as module_name
		FROM tickets t
		LEFT JOIN users u ON t.assignee_user_id = u.id
		INNER JOIN elements e ON t.element_id = e.id
		INNER JOIN modules m ON e.module_id = m.id
		INNER JOIN ticket_priority p ON t.priority_id = p.id
		LEFT JOIN customer_segment cs ON t.customer_segment_id = cs.id
		LEFT JOIN ticket_requestor tr ON t.ticket_requestor_id = tr.id
		INNER JOIN ticket_status s ON t.ticket_status_id = s.id
	`
	query, args := storage.BuildQuery(baseQuery, conditions...)

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errorwrapper.Error(apiError.ResourceNotFound, "tickets not found")
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	defer rows.Close()

	var tickets []TicketDTO
	for rows.Next() {
		var t TicketDTO
		var data string
		err := rows.Scan(
			&t.ID, &t.ElementID, &t.PriorityID, &t.DeadlineTime, &t.TicketStatusID,
			&data, &t.CreatedAt, &t.UpdatedAt, &t.CreatedBy, &t.UpdatedBy,
			&t.Source, &t.AssigneeUserName, &t.ElementName, &t.PriorityName, &t.CaseCategory, &t.CaseSubcategory, &t.TicketCloseDatetime, &t.DomainID,
			&t.Channel, &t.TicketRequestorID, &t.TicketRequestorName, &t.CustomerSegmentID, &t.CustomerSegmentName, &t.ParentTicketID, &t.StatusName, &t.ModuleName,
		)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		err = json.Unmarshal([]byte(data), &t.Data)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		tickets = append(tickets, t)
	}

	return tickets, nil
}

// GetTicketCount retrieves total count of tickets based on conditions
func GetTicketCount(ctx context.Context, db *sql.DB, conditions []storage.QueryCondition) (int64, error) {
	baseQuery := `SELECT COUNT(*) FROM tickets t
	INNER JOIN elements e ON t.element_id = e.id`

	query, args := storage.BuildQuery(baseQuery, conditions...)

	var count int64
	err := db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return count, nil
}

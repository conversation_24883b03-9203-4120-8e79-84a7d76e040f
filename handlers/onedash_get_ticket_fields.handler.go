package handlers

import (
	context "context"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
)

// GetTicketFields is API to get ticket fields configuration
func (o *OnedashService) GetTicketFields(ctx context.Context, req *api.GetTicketFieldsRequest) (*api.GetTicketFieldsResponse, error) {
	result, err := logic.Process.GetTicketFields(ctx, req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket fields")
	}

	return result, nil
}

package api

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	accountServiceMock "gitlab.super-id.net/bersama/core-banking/account-service/api/mock"
	customerExperience "gitlab.super-id.net/bersama/onboarding/customer-experience/api"
	customerExperienceMock "gitlab.super-id.net/bersama/onboarding/customer-experience/api/mock"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	customerMaster "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2"
	customerMasterMock "gitlab.super-id.net/bersama/onboarding/customer-master/api/v2/mock"

	customerJournal "gitlab.super-id.net/bersama/corex/customer-journal/api"
	customerJournalMock "gitlab.super-id.net/bersama/corex/customer-journal/api/mock"
	amlService "gitlab.super-id.net/bersama/fintrust/aml-service/api"
	amlServiceMock "gitlab.super-id.net/bersama/fintrust/aml-service/api/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	grabID "gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid"
	grabIDMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/grabid/mock"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search", func() {
	var (
		db     *sql.DB
		mocker sqlmock.Sqlmock
	)

	BeforeEach(func() {
		var err error
		db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockAmlServiceCustomer = &amlServiceMock.Customer{}
		mockAccountService = &accountServiceMock.AccountService{}
		mockCustomerJournal = &customerJournalMock.CustomerJournal{}
		mockGrabID = &grabIDMock.GrabID{}

		config := &logic.MockProcessConfig{
			CustomerMaster:     mockCustomerMaster,
			CustomerExperience: mockCustomerExperience,
			AmlServiceCustomer: mockAmlServiceCustomer,
			AccountService:     mockAccountService,
			AppConfig:          service.AppConfig,
			CustomerJournal:    mockCustomerJournal,
			GrabID:             mockGrabID,
		}
		logic.MockInitLogic(config)
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		auditTrailLogic.MockInitLogic(service.AppConfig)
		permissionManagementLogic.MockInitLogic(service.AppConfig)
	})

	AfterEach(func() {
		db.Close()
	})

	Context("CustomerSearch", func() {
		When("user has valid permissions", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("", 1). // keyword and status
					WillReturnRows(&sqlmock.Rows{})

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(
						1, "Customer Related Data", "CUSTOMER_RELATED_DATA", "tab", nil, 1,
					))

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"type": "root",
						"children": [
							{
								"key": "CUSTOMER_RELATED_DATA",
								"type": "tab",
								"label": "Customer Related Data"
							}
						]
					},
					"data": {
						"meta": null
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user does not have permissions", func() {
			It("should return forbidden error", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				permDetail := resources.SampleDataUserPermissionsDetail()
				binaryData, _ := json.Marshal(permDetail)
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				res, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(resources.ForbiddenElementErrorResponse))
			})
		})

		When("user has valid permissions and getting tab details", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "customerHomepage"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("customerHomepage", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Customer Related Data", "customerHomepage", nil,
						1, 1, 1, "tab",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(
						1, "Address Details", "addressDetails", "sectionArray", 1, 1,
					).AddRow(2, "Contact Details", "contactDetails", "sectionArray", 1, 2))

				// expectedResponseForGetCustomerLean ...
				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"publicID": "ID198980161816",
							"name": "Mrs Nathanael Buckridge",
							"gender": "MALE",
							"dateOfBirth": "1998-08-18",
							"nationality": "WNI",
							"status": "ONBOARDED",
							"type": "NAT_PERSON",
							"startDate": "2024-01-30T04:38:20Z",
							"relatedCounterPartyInd": false,
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"placeOfBirth": "PEMALANG"
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "customerHomepage",
						"type": "tab",
						"label": "Customer Related Data",
						"children": [
							{
								"key": "addressDetails",
								"type": "sectionArray",
								"label": "Address Details"
							},
							{
								"key": "contactDetails",
								"type": "sectionArray",
								"label": "Contact Details"
							}
						]
					},
					"data": {
						"cif": "ID198980161816",
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting address details", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "addressDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("addressDetails", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Address Details", "addressDetails", nil, 1, 2, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(
						1, "RT", "rt", "oneliner", 1, 1,
					).AddRow(2, "Village", "village", "oneliner", 1, 2))

				// expectedResponseForGetCustomerLean ...
				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"publicID": "ID198980161816",
							"name": "Mrs Nathanael Buckridge",
							"gender": "MALE",
							"dateOfBirth": "1998-08-18",
							"nationality": "WNI",
							"status": "ONBOARDED",
							"type": "NAT_PERSON",
							"startDate": "2024-01-30T04:38:20Z",
							"relatedCounterPartyInd": false,
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"placeOfBirth": "PEMALANG",
							"addresses": [
								{
									"addressType": "REGISTERED",
									"street": "JL RAYA CISEENG NO. 12 BLOK A",
									"city": "KABUPATEN BOGOR",
									"country": "ID",
									"postalCode": "16120",
									"province": "JAWA BARAT",
									"subdistrict": "CISEENG",
									"village": "CIBENTANG",
									"rt": "001",
									"rw": "002",
									"provinceKey": "JAWA BARAT_32",
									"cityKey": "BOGOR_32.01",
									"subdistrictKey": "CISEENG_32.01.33",
									"villageKey": "CIBENTANG_32.01.33.2004",
									"postalCodeKey": "16120_32.01.33.2004"
								},
								{
									"addressType": "MAILING",
									"street": "JL RAYA CISEENG NO. 12 BLOK A",
									"city": "KABUPATEN BOGOR",
									"country": "ID",
									"postalCode": "16120",
									"province": "JAWA BARAT",
									"subdistrict": "CISEENG",
									"village": "CIBENTANG",
									"rt": "001",
									"rw": "002",
									"provinceKey": "JAWA BARAT_32",
									"cityKey": "BOGOR_32.01",
									"subdistrictKey": "CISEENG_32.01.33",
									"villageKey": "CIBENTANG_32.01.33.2004",
									"postalCodeKey": "16120_32.01.33.2004"
								}
							]
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo, customerMaster.Data_Addresses},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "addressDetails",
						"type": "sectionArray",
						"label": "Address Details",
						"children": [
							{
								"key": "rt",
								"type": "oneliner",
								"label": "RT"
							},
							{
								"key": "village",
								"type": "oneliner",
								"label": "Village"
							}
						]
					},
					"data": {
						"addressDetails": [
							{
								"addressDetails": null,
								"rt": "001",
								"village": "CIBENTANG"
							},
							{
								"addressDetails": null,
								"rt": "001",
								"village": "CIBENTANG"
							}
						],
						"cif": "ID198980161816",
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting contact details", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "contactDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("contactDetails", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Contact Details", "contactDetails", nil, 1, 2, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(
						1, "Phone Number", "phoneNumber", "oneliner", 1, 1,
					))

				// expectedResponseForGetCustomerLean ...
				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"publicID": "ID198980161816",
							"name": "Mrs Nathanael Buckridge",
							"gender": "MALE",
							"dateOfBirth": "1998-08-18",
							"nationality": "WNI",
							"status": "ONBOARDED",
							"type": "NAT_PERSON",
							"startDate": "2024-01-30T04:38:20Z",
							"relatedCounterPartyInd": false,
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"placeOfBirth": "PEMALANG",
							"contacts": [
								{
									"contactType": "PRIMARY",
									"email": "",
									"phoneNumber": "+************"
								}
							]
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo, customerMaster.Data_Contacts},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "contactDetails",
						"type": "sectionArray",
						"label": "Contact Details",
						"children": [
							{
								"key": "phoneNumber",
								"type": "oneliner",
								"label": "Phone Number"
							}
						]
					},
					"data": {
						"cif": "ID198980161816",
						"meta": null,
						"contactDetails": [
							{
								"contactDetails": null,
								"phoneNumber": "+************"
							}
						],
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting customer details", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "customerDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("customerDetails", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Customer Details", "customerDetails", nil, 1, 2, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(
						1, "Name", "fullName", "oneliner", 1, 1,
					))

				// expectedResponseForCustomerDetailsByIdentifierV2 ...
				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
					"items": [
						{
							"customer": {
								"CIF": "ID195849217102",
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
								"NIK": "****************",
								"addresses": null,
								"alias": "",
								"contacts": null,
								"dateOfBirth": "1991-07-16",
								"ecosystemsMapping": null,
								"employments": {
									"NPWP": "",
									"address": "editable company address",
									"employerName": "editable company name",
									"industrySector": "Pendidikan",
									"jobPosition": "Staf",
									"monthlyIncome": "Rp 5 juta",
									"occupation": "Eksekutif",
									"purposeOfAccount": "Gaji",
									"sourceOfFunds": "Pendapatan Usaha",
									"workPhoneNumber": "*************"
								},
								"fullName": "ADE SYABRINA IRMA",
								"gender": "FEMALE",
								"isECDD": false,
								"isPEP": false,
								"isECDD": false,
								"isPEP": false,
								"makerName": "",
								"maritalStatus": "MARRIED",
								"motherMaidenName": "nama ibu",
								"nationality": "WNI",
								"onboardingChannel": "MANUAL",
								"placeOfBirth": "IF JAKARTA",
								"startDate": "0001-01-01T00:00:00Z",
								"status": "ONBOARDED",
								"type": "NAT_PERSON"
							},
							"onboardingAttemptTime": 1
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockResponse)

				// mock for GetCustomerOps
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": null,
						"riskAssessment": null,
						"bo": null,
						"ecdd": null,
						"recdd": null
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					Data:       []string{"nameScreening", "riskRating", "bo", "ecdd"},
				}).Return(mockResponseAml, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
					  "key": "customerDetails",
					  "type": "section",
					  "label": "Customer Details",
					  "children": [
						{
							"key": "fullName",
							"type": "oneliner",
							"label": "Name"
						}
					  ]
					},
					"data": {
						"cif": "ID195849217102",
						"meta": null,
						"fullName": "ADE SYABRINA IRMA",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				  }`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting occupation details", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "occupationDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("occupationDetails", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Occupation Details", "occupationDetails", nil, 1, 2, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(
						1, "Employer Name", "employerName", "oneliner", 1, 1,
					))

				// expectedResponseForCustomerDetailsByIdentifierV2 ...
				var expectedResponseForCustomerDetailsByIdentifierV2 = `{
					"items": [
						{
							"customer": {
								"CIF": "ID195849217102",
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
								"NIK": "****************",
								"addresses": null,
								"alias": "",
								"contacts": null,
								"dateOfBirth": "1991-07-16",
								"ecosystemsMapping": null,
								"employments": {
									"NPWP": "",
									"address": "editable company address",
									"employerName": "editable company name",
									"industrySector": "Pendidikan",
									"jobPosition": "Staf",
									"monthlyIncome": "Rp 5 juta",
									"occupation": "Eksekutif",
									"purposeOfAccount": "Gaji",
									"sourceOfFunds": "Pendapatan Usaha",
									"workPhoneNumber": "*************"
								},
								"fullName": "ADE SYABRINA IRMA",
								"gender": "FEMALE",
								"isECDD": false,
								"isPEP": false,
								"isECDD": false,
								"isPEP": false,
								"makerName": "",
								"maritalStatus": "MARRIED",
								"motherMaidenName": "nama ibu",
								"nationality": "WNI",
								"onboardingChannel": "MANUAL",
								"placeOfBirth": "IF JAKARTA",
								"startDate": "0001-01-01T00:00:00Z",
								"status": "ONBOARDED",
								"type": "NAT_PERSON"
							},
							"onboardingAttemptTime": 1
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForCustomerDetailsByIdentifierV2), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": null,
						"riskAssessment": null,
						"bo": null,
						"ecdd": null,
						"recdd": null
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					Data:       []string{"nameScreening", "riskRating", "bo", "ecdd"},
				}).Return(mockResponseAml, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
					  "key": "occupationDetails",
					  "type": "section",
					  "label": "Occupation Details",
					  "children": [
						{
							"key": "employerName",
							"type": "oneliner",
							"label": "Employer Name"
						}
					  ]
					},
					"data": {
						"cif": "ID195849217102",
						"meta": null,
						"employerName": "editable company name",
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				  }`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("invalid request body", func() {
			It("should return bad request error", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "",
					"identifierType": "invalid",
					"key": "invalid"
				}`)

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(400))
			})
		})

		When("getting address details with employment type", func() {
			It("should skip employment addresses in response", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "addressDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("addressDetails", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Address Details", "addressDetails", nil,
						1, 2, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Street", "street", "oneliner", 1, 1).
						AddRow(2, "City", "city", "oneliner", 1, 2).
						AddRow(3, "RT", "rt", "oneliner", 1, 3).
						AddRow(4, "RW", "rw", "oneliner", 1, 4))

				var customerResponse = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"addresses": [
								{
									"addressType": "EMPLOYMENT",
									"street": "123",
									"city": "City asal"
								},
								{
									"addressType": "MAILING",
									"street": "Home 456",
									"city": "Home City",
									"rt": "001",
									"rw": "002"
								}
							]
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(customerResponse), &mockResponse)

				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo, customerMaster.Data_Addresses},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "addressDetails",
						"type": "sectionArray",
						"label": "Address Details",
						"children": [
							{
								"key": "street",
								"type": "oneliner",
								"label": "Street"
							},
							{
								"key": "city",
								"type": "oneliner",
								"label": "City"
							},
							{
								"key": "rt",
								"type": "oneliner",
								"label": "RT"
							},
							{
								"key": "rw",
								"type": "oneliner",
								"label": "RW"
							}
						]
					},
					"data": {
						"addressDetails": [
							{
								"addressDetails": null,
								"city": "Home City",
								"rt": "001",
								"rw": "002",
								"street": "Home 456"
							}
						],
						"cif": null,
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("customer master returns error", func() {
			It("should return internal server error", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "customerHomepage"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mockCustomerMaster.On("GetCustomerLean", mock.Anything, mock.Anything).Return(nil, errors.New("upstream error")).Once()

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(500))
			})
		})

		When("getting onboarding details", func() {
			It("should return onboarding details", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "onboardingDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("onboardingDetails", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Onboarding Details", "onboardingDetails", nil,
						1, 2, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(1, "Expires At", "expiresAt", "datetime", 1, 1).
						AddRow(2, "Created At", "createdAt", "datetime", 1, 2).
						AddRow(3, "Updated At", "updatedAt", "datetime", 1, 3).
						AddRow(4, "Submitted At", "submittedAt", "datetime", 1, 4))

				var customerResponse = `{
					"items": [
						{
							"customer": {
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
							},
							"applications": [
								{
									"ID": "1c1b941f-278e-4399-8933-130c09258f0f",
									"expiresAt": "2023-07-12T10:24:28Z",
									"createdAt": "2023-07-05T10:22:14Z",
									"updatedAt": "2024-06-05T10:40:17Z",
									"submittedAt": "2023-07-05T10:24:23Z"
								}
							]
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(customerResponse), &mockResponse)

				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": null,
						"riskAssessment": null,
						"bo": null,
						"ecdd": null,
						"recdd": null
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					Data:       []string{"nameScreening", "riskRating", "bo", "ecdd"},
				}).Return(mockResponseAml, nil).Once()

				// mock for GetCustomerJournalData
				expectedCustomerJournalResponse := `{
						"links": {
								"prevCursorID": "eyJjdXN0b21lcklEIjoiODFiMTJhMDUtYTAzNS00ODg4LWFjNzAtZDc4NTY1ZDk0Y2IwIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzUwMjM1ODk2MzgyNDM4MDAwIn0="
						},
						"data": [
								{
										"customerID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
										"eventTimestamp": "1750235896382438000",
										"metadata": {
												"qcCompleted": "false",
												"qcResult": "Pending"
										}
								}
						]
				}`

				var mockJournalResponse *customerJournal.Response
				_ = json.Unmarshal([]byte(expectedCustomerJournalResponse), &mockJournalResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournal.Request{
					UserSafeID: "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					PageSize:   1,
					Endpoint:   constants.CustomerJournalLogTypeQCFlag,
				}).Return(mockJournalResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "onboardingDetails",
						"type": "section",
						"label": "Onboarding Details",
						"children": [
							{
							  "key": "expiresAt",
							  "type": "datetime",
							  "label": "Expires At"
							},
							{
							  "key": "createdAt",
							  "type": "datetime",
							  "label": "Created At"
							},
							{
							  "key": "updatedAt",
							  "type": "datetime",
							  "label": "Updated At"
							},
							{
							  "key": "submittedAt",
							  "type": "datetime",
							  "label": "Submitted At"
							}
						]
					},
					"data": {
						"cif": "",
						"meta": null,
						"createdAt": "2023-07-05T10:22:14Z",
					  	"expiresAt": "2023-07-12T10:24:28Z",
					  	"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					  	"submittedAt": "2023-07-05T10:24:23Z",
					  	"updatedAt": "2024-06-05T10:40:17Z"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("segregation detail query returns error", func() {
			It("should return internal server error", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "customerDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("customerDetails", 1).
					WillReturnError(errors.New("database error"))

				var customerResponse = `{
					"items": [
						{
							"customer": {
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
							}
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(customerResponse), &mockResponse)

				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": null,
						"riskAssessment": null,
						"bo": null,
						"ecdd": null,
						"recdd": null
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					Data:       []string{"nameScreening", "riskRating", "bo", "ecdd"},
				}).Return(mockResponseAml, nil).Once()

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(500))
			})
		})

		When("user role query returns error", func() {
			It("should return internal server error", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnError(errors.New("database error"))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(500))
			})
		})

		When("getting address details from customer experience", func() {
			It("should return onboarding details", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).NotTo(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "addressDetails"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("addressDetails", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Address Details", "addressDetails", nil,
						1, 2, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(1, "Street", "street", "oneliner", 1, 1).
						AddRow(2, "City", "city", "oneliner", 1, 2).
						AddRow(3, "RT", "rt", "oneliner", 1, 3).
						AddRow(4, "RW", "rw", "oneliner", 1, 4))

				var customerResponse = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"addresses": []
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(customerResponse), &mockResponse)

				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo, customerMaster.Data_Addresses},
				}).Return(mockResponse, nil).Once()

				// expectedResponseForGetCustomerOps ...
				var expectedResponseForGetCustomerOps = `{
					"items": [
						{
							"customer": {
								"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
								"addresses": [
									{
										"addressType": "REGISTERED",
										"street": "JL RAYA CISEENG NO. 12 BLOK A",
										"city": "KABUPATEN BOGOR",
										"country": "ID",
										"postalCode": "16120",
										"province": "JAWA BARAT",
										"subdistrict": "CISEENG",
										"village": "CIBENTANG",
										"rt": "001",
										"rw": "002",
										"provinceKey": "JAWA BARAT_32",
										"cityKey": "BOGOR_32.01",
										"subdistrictKey": "CISEENG_32.01.33",
										"villageKey": "CIBENTANG_32.01.33.2004",
										"postalCodeKey": "16120_32.01.33.2004"
									},
									{
										"addressType": "MAILING",
										"street": "JL RAYA CISEENG NO. 12 BLOK A",
										"city": "KABUPATEN BOGOR",
										"country": "ID",
										"postalCode": "16120",
										"province": "JAWA BARAT",
										"subdistrict": "CISEENG",
										"village": "CIBENTANG",
										"rt": "001",
										"rw": "002",
										"provinceKey": "JAWA BARAT_32",
										"cityKey": "BOGOR_32.01",
										"subdistrictKey": "CISEENG_32.01.33",
										"villageKey": "CIBENTANG_32.01.33.2004",
										"postalCodeKey": "16120_32.01.33.2004"
									}
								]
							}
						}
					]
				}`

				var mockResponseOps *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerOps), &mockResponseOps)

				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponseOps, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": null,
						"riskAssessment": null,
						"bo": null,
						"ecdd": null,
						"recdd": null
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					Data:       []string{"nameScreening", "riskRating", "bo", "ecdd"},
				}).Return(mockResponseAml, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "addressDetails",
						"type": "sectionArray",
						"label": "Address Details",
						"children": [
							{
								"key": "street",
								"type": "oneliner",
								"label": "Street"
							},
							{
								"key": "city",
								"type": "oneliner",
								"label": "City"
							},
							{
								"key": "rt",
								"type": "oneliner",
								"label": "RT"
							},
							{
								"key": "rw",
								"type": "oneliner",
								"label": "RW"
							}
						]
					},
					"data": {
						"addressDetails": [
							{
								"addressDetails": null,
								"city": "KABUPATEN BOGOR",
								"rt": "001",
								"rw": "002",
								"street": "JL RAYA CISEENG NO. 12 BLOK A"
							},
							{
								"addressDetails": null,
								"city": "KABUPATEN BOGOR",
								"rt": "001",
								"rw": "002",
								"street": "JL RAYA CISEENG NO. 12 BLOK A"
							}
						],
						"cif": "",
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return customer enhanced due diligence results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "a5647018-a2c1-4aec-b9a5-a60b2e497865",
					"identifierType": "SAFE_ID",
					"key": "customerEnhancedDueDiligence"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("customerEnhancedDueDiligence", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Customer Enhanced Due Diligence", "customerEnhancedDueDiligence", nil, 1, 2, 1, "sectionQnA",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(1, "Question", "question", "oneliner", 1, 1).
						AddRow(2, "Answer", "answer", "oneliner", 1, 2))

				var customerResponse = `{
					"items": [
						{
							"customer": {
								"ID": "a5647018-a2c1-4aec-b9a5-a60b2e497865",
								"publicID": "ID174045389072"
							}
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(customerResponse), &mockResponse)

				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "a5647018-a2c1-4aec-b9a5-a60b2e497865",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "a5647018-a2c1-4aec-b9a5-a60b2e497865",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": null,
						"riskAssessment": null,
						"bo": null,
						"ecdd": {
							"data": [
								{
									"questionID": "authority_figure",
									"questionDescription": "Are you a politically exposed person or related to one?",
									"answerDescription": "No"
								},
								{
									"questionID": "alternative_income",
									"questionDescription": "Do you have alternative sources of income?",
									"answerDescription": "Yes, from investments"
								}
							]
						},
						"recdd": null
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "a5647018-a2c1-4aec-b9a5-a60b2e497865",
					Data:       []string{"nameScreening", "riskRating", "bo", "ecdd"},
				}).Return(mockResponseAml, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "customerEnhancedDueDiligence",
						"type": "sectionQnA",
						"label": "Customer Enhanced Due Diligence",
						"children": [
							{
								"key": "question",
								"type": "oneliner",
								"label": "Question"
							},
							{
								"key": "answer",
								"type": "oneliner",
								"label": "Answer"
							},
							{
								"key": "question",
								"type": "oneliner",
								"label": "Question"
							},
							{
								"key": "answer",
								"type": "oneliner",
								"label": "Answer"
							}
						]
					},
					"data": {
						"cif": "",
						"meta": null,
						"safeID": "a5647018-a2c1-4aec-b9a5-a60b2e497865"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})

			It("should successfully return customer re-enhanced due diligence results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "a5647018-a2c1-4aec-b9a5-a60b2e497865",
					"identifierType": "SAFE_ID",
					"key": "recdd"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("recdd", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Re-CDD Information", "recdd", nil, 1, 2, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).AddRow(1, "Re-CDD Flag", "recddFlag", "oneliner", 1, 1).
						AddRow(2, "Latest Re-CDD Date", "recddLatestDate", "date", 1, 2).
						AddRow(3, "Next Re-CDD Date", "recddNextDate", "date", 1, 3))

				var customerResponse = `{
					"items": [
						{
							"customer": {
								"ID": "a5647018-a2c1-4aec-b9a5-a60b2e497865",
								"publicID": "ID174045389072"
							}
						}
					]
				}`

				var mockResponse *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(customerResponse), &mockResponse)

				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "a5647018-a2c1-4aec-b9a5-a60b2e497865",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponse, nil).Once()

				var expectedResponseForGetCustomerData = `{
					"data": {
						"identifier": "a5647018-a2c1-4aec-b9a5-a60b2e497865",
						"nameScreening": null,
						"riskRating": null,
						"customerFlag": {
							"isReCDD": true
						},
						"riskAssessment": null,
						"bo": null,
						"ecdd": null,
						"recdd": {
							"latestReCDDDate": "2025-06-16",
							"nextReCDDDate": "2028-06-16"
						}
					}
				}`

				var mockResponseAml *amlService.GetCustomerResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerData), &mockResponseAml)

				// mock for GetCustomerData with recdd and customerFlag data types
				mockAmlServiceCustomer.On("GetCustomerData", mock.Anything, &amlService.GetCustomerRequest{
					Identifier: "a5647018-a2c1-4aec-b9a5-a60b2e497865",
					Data:       []string{"recdd", "customerFlag"},
				}).Return(mockResponseAml, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "recdd",
						"type": "section",
						"label": "Re-CDD Information",
						"children": [
							{
								"key": "recddFlag",
								"type": "oneliner",
								"label": "Re-CDD Flag"
							},
							{
								"key": "recddLatestDate",
								"type": "date",
								"label": "Latest Re-CDD Date"
							},
							{
								"key": "recddNextDate",
								"type": "date",
								"label": "Next Re-CDD Date"
							}
						]
					},
					"data": {
						"cif": "ID174045389072",
						"meta": null,
						"recddFlag": "True",
						"recddLatestDate": "2025-06-16",
						"recddNextDate": "2028-06-16",
						"safeID": "a5647018-a2c1-4aec-b9a5-a60b2e497865"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	When("getting customer summary data", func() {
		It("should return customer summary data", func() {
			token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
				"id": resources.TestCustomerSearch,
			}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
			Expect(err).NotTo(HaveOccurred())

			xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
			body := hcl.JSON(`{
				"identifier": "1234567890",
				"identifierType": "CIF",
				"key": "customerSummary"
			}`)

			mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

			binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
			mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

			mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
				WithArgs(int64(1)).
				WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
					AddRow(1, "ADMIN"))

			mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
				WithArgs("customerSummary", 1).
				WillReturnRows(sqlmock.NewRows([]string{
					"id", "name", "keyword", "parent_segregation_id",
					"order", "level", "status", "type",
				}).AddRow(
					1, "Customer Summary", "customerSummary", nil,
					1, 2, 1, "section",
				))

			mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
				WillReturnRows(sqlmock.NewRows([]string{
					"id", "name", "keyword", "type", "parent_segregation_id", "order",
				}).AddRow(1, "Customer Name", "custSummaryCustomerName", "oneliner", 1, 1).
					AddRow(2, "CIF", "custSummaryCif", "oneliner", 1, 2).
					AddRow(3, "Customer Status", "custSummaryCustomerStatus", "oneliner", 1, 3).
					AddRow(4, "Application Status", "custSummaryApplicationStatus", "oneliner", 1, 4).
					AddRow(5, "Safe ID", "custSummarySafeId", "oneliner", 1, 5).
					AddRow(6, "Phone Number", "custSummaryPhoneNumber", "oneliner", 1, 6).
					AddRow(7, "Access Status", "custSummaryAccessStatus", "oneliner", 1, 7))

			// Mock customer data response
			customerResponse := `{
				"items": [
					{
						"customer": {
							"ID": "safe-id-12345",
							"CIF": "1234567890",
							"FullName": "John Doe",
							"Status": "ACTIVE",
							"Contacts": [
								{
									"PhoneNumber": "+6281234567890"
								}
							]
						},
						"applications": [
							{
								"Status": "APPROVED"
							}
						]
					}
				]
			}`

			var mockCustomerResponse *customerExperience.GetCustomerOpsResponse
			_ = json.Unmarshal([]byte(customerResponse), &mockCustomerResponse)

			mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
				Identifier:     "1234567890",
				IdentifierType: "CIF",
				Page:           1,
			}).Return(mockCustomerResponse, nil).Once()

			// Mock customer access status
			accessResponse := &grabID.GetUserStatusResponse{
				Status: "ACTIVE",
			}

			mockGrabID.On("GetUserStatus", mock.Anything, mock.Anything).Return(accessResponse.Status, nil).Once()

			expectedResponse := `{
				"status": "Success",
				"structure": {
					"key": "customerSummary",
					"type": "section",
					"label": "Customer Summary",
					"children": [
						{
							"key": "custSummaryCustomerName",
							"type": "oneliner",
							"label": "Customer Name"
						},
						{
							"key": "custSummaryCif",
							"type": "oneliner",
							"label": "CIF"
						},
						{
							"key": "custSummaryCustomerStatus",
							"type": "oneliner",
							"label": "Customer Status"
						},
						{
							"key": "custSummaryApplicationStatus",
							"type": "oneliner",
							"label": "Application Status"
						},
						{
							"key": "custSummarySafeId",
							"type": "oneliner",
							"label": "Safe ID"
						},
						{
							"key": "custSummaryPhoneNumber",
							"type": "oneliner",
							"label": "Phone Number"
						},
						{
							"key": "custSummaryAccessStatus",
							"type": "oneliner",
							"label": "Access Status"
						}
					]
				},
				"data": {
					"customerSummary": [
						{
							"custSummaryCustomerName": "John Doe",
							"custSummaryCif": "1234567890",
							"custSummaryCustomerStatus": "ACTIVE",
							"custSummaryApplicationStatus": "APPROVED",
							"custSummarySafeId": "safe-id-12345",
							"custSummaryPhoneNumber": "+6281234567890",
							"custSummaryAccessStatus": "ACTIVE"
						}
					]
				}
			}`

			response, err := client.Post(CustomerSearch, body, xfccHeader)

			Expect(err).ShouldNot(HaveOccurred())
			Expect(response).ShouldNot(BeNil())
			Expect(response.StatusCode).Should(Equal(200))
			Expect(response.Body.String).Should(MatchJSON(expectedResponse))
		})
	})

	When("customer data is not found", func() {
		It("should return empty response", func() {
			token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
				"id": resources.TestCustomerSearch,
			}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
			Expect(err).NotTo(HaveOccurred())

			xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
			body := hcl.JSON(`{
				"identifier": "1234567890",
				"identifierType": "CIF",
				"key": "customerSummary"
			}`)

			mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

			binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
			mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

			mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
				WithArgs(int64(1)).
				WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
					AddRow(1, "ADMIN"))

			mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
				WithArgs("customerSummary", 1).
				WillReturnRows(sqlmock.NewRows([]string{
					"id", "name", "keyword", "parent_segregation_id",
					"order", "level", "status", "type",
				}).AddRow(
					1, "Customer Summary", "customerSummary", nil,
					1, 2, 1, "section",
				))

			// Empty customer response
			emptyResponse := `{"items": []}`
			var mockCustomerResponse *customerExperience.GetCustomerOpsResponse
			_ = json.Unmarshal([]byte(emptyResponse), &mockCustomerResponse)

			mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
				Identifier:     "1234567890",
				IdentifierType: "CIF",
				Page:           1,
			}).Return(mockCustomerResponse, nil).Once()

			expectedResponse := `{
				"status": "Success",
				"structure": {
					"key": "customerSummary",
					"type": "section",
					"label": "Customer Summary",
					"children": []
				},
				"data": {}
			}`

			response, err := client.Post(CustomerSearch, body, xfccHeader)

			Expect(err).ShouldNot(HaveOccurred())
			Expect(response).ShouldNot(BeNil())
			Expect(response.StatusCode).Should(Equal(200))
			Expect(response.Body.String).Should(MatchJSON(expectedResponse))
		})
	})
})

package api

import (
	"database/sql"
	"encoding/json"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

const GetTicketFields = "/api/v1/ticket-fields"

var _ = Describe("Get Ticket Fields API", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
	})

	Context("Success request", func() {
		When("get ticket fields", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				Expect(err).ShouldNot(HaveOccurred())

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				// Mock user permissions in Redis
				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsDetail())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestUserID).Return(string(binaryData), nil)

				// Mock DB queries for element, priorities, ticket requestors, etc. (minimal for now)
				mocker.ExpectQuery(regexp.QuoteMeta("SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE id = ?")).
					WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "created_at", "created_by", "updated_at", "updated_by", "module_id", "default_priority_id", "code", "status", "has_ticketing", "default_ticket_requestor_id", "default_customer_segment_id"}).
						AddRow(1, "Element1", sql.NullTime{Valid: true}, 1, sql.NullTime{Valid: true}, 2, 1, 1, "BLOCK_ACCOUNT", 1, true, 1, 1))

				// Add more mocks as needed for priorities, ticket requestors, etc.

				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := map[string]interface{}{
					"elementID": 1,
				}

				response, err := client.Get(GetTicketFields, hcl.JSON(body), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))

			})
		})
	})

	Context("Error request", func() {
		When("internal server error occurs", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				Expect(err).ShouldNot(HaveOccurred())

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)
				// Simulate DB error on element query
				mocker.ExpectQuery(regexp.QuoteMeta("SELECT id, name, created_at, created_by, updated_at, updated_by, module_id, default_priority_id, code, status, has_ticketing, default_ticket_requestor_id, default_customer_segment_id FROM elements WHERE id = ?")).
					WithArgs(1).
					WillReturnError(sql.ErrConnDone)

				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestUserID,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := map[string]interface{}{
					"elementID": 1,
				}

				response, err := client.Get(GetTicketFields, hcl.JSON(body), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(response.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
			})
		})
	})
})

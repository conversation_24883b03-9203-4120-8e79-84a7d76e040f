package api

import (
	"database/sql"
	"errors"
	"log"
	"regexp"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud"
	jcMock "gitlab.super-id.net/bersama/opsce/onedash-be/external/jumpcloud/mocks"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
)

var _ = Describe("Login", func() {
	BeforeEach(func() {
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		mockRedis = &redisMock.Client{}
		redis.RedisClient = mockRedis
		mockJumpcloud = &jcMock.JumpCloud{}
		jumpcloud.JCClient = mockJumpcloud

		appConfig := service.AppConfig
		appConfig.FeatureFlag = config.FeatureFlag{
			UseNativeLogin:          true,
			UseConcurrentLoginLimit: false,
		}

		config := &logic.MockProcessConfig{
			AppConfig: appConfig,
		}

		logic.MockInitLogic(config)
	})

	Context("Success request", func() {
		When("success native login", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				sampleUserData := resources.SampleDataUser()

				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "password",
					"type": "NATIVE"
				}`)

				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs(sampleUserData.Email).WillReturnRows(resources.SampleRowsGetUserByEmail(mocker, sampleUserData))
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesUpdateUserExpiry)).WithArgs(sqlmock.AnyArg(), 1, sampleUserData.UserID).WillReturnResult(sqlmock.NewResult(1, 1))

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, resources.SampleDataUser()))
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(sampleUserData.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				auditTrail := resources.SampleDataUserLoginAuditTrail(sampleUserData)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+sampleUserData.UserID).Return(true, nil)
				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})
		When("success jumpcloud login", func() {
			It("return 200", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				sampleUserData := resources.SampleDataUser()

				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"token": "tokenize",
					"type": "JUMPCLOUD"
				}`)

				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs(sampleUserData.Email).WillReturnRows(resources.SampleRowsGetUserByEmail(mocker, sampleUserData))
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesUpdateUserExpiry)).WithArgs(sqlmock.AnyArg(), 1, sampleUserData.UserID).WillReturnResult(sqlmock.NewResult(1, 1))

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(resources.TestUserID).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, resources.SampleDataUser()))
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(sampleUserData.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				auditTrail := resources.SampleDataUserLoginAuditTrail(sampleUserData)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(1, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				mockRedis.On("Delete", mock.Anything, constants.UserIDRedisKey+sampleUserData.UserID).Return(true, nil)
				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				mockJumpcloud.On("VerifyEmail", mock.Anything, mock.Anything, mock.Anything).Return(jumpcloud.UserInfo{Email: "<EMAIL>"}, nil)

				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})
	})
	Context("Invalid Request", func() {
		When("invalid request - missing type", func() {
			It("return 400", func() {
				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "password"
				}`)

				expectedErr := `{
					"code":"badRequest",
					"message":"failed to validate request Key: 'LoginRequest.Type' Error:Field validation for 'Type' failed on the 'required' tag"
				}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("native - empty password", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				sampleUserData := resources.SampleDataUser()
				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "",
					"type": "NATIVE"
				}`)

				// get user by email
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs(sampleUserData.Email).WillReturnRows(mocker.NewRows([]string{"id", "name", "email", "password", "status", "user_id", "created_at", "updated_at", "created_by", "updated_by"}).AddRow(
					sampleUserData.ID, sampleUserData.Name, sampleUserData.Email, sampleUserData.Password, sampleUserData.Status, sampleUserData.UserID, sampleUserData.CreatedAt, sampleUserData.UpdatedAt, sampleUserData.CreatedBy, sampleUserData.UpdatedBy))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedErr := `{
					"code": "fieldMissing",
					"message": "missing password"
				}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.FieldMissing.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("native - false password", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				sampleUserData := resources.SampleDataUser()
				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "false-password",
					"type": "NATIVE"
				}`)

				// get user by email
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs(sampleUserData.Email).WillReturnRows(mocker.NewRows([]string{"id", "name", "email", "password", "status", "user_id", "created_at", "updated_at", "created_by", "updated_by"}).AddRow(
					sampleUserData.ID, sampleUserData.Name, sampleUserData.Email, sampleUserData.Password, sampleUserData.Status, sampleUserData.UserID, sampleUserData.CreatedAt, sampleUserData.UpdatedAt, sampleUserData.CreatedBy, sampleUserData.UpdatedBy))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedErr := `{
					"code": "badRequest",
					"message": "Invalid password"
				}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.BadRequest.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("native - invalid user", func() {
			It("return 400", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "false-password",
					"type": "NATIVE"
				}`)

				// get user by email
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrNoRows)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedErr := `{
					"code": "resourceNotFound",
					"message": "Email not found, err: sql: no rows in result set"
				}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.ResourceNotFound.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("inactive user", func() {
			It("return 403", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				sampleUserData := resources.SampleDataUser()
				sampleUserData.Status = 0

				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "password",
					"type": "NATIVE"
				}`)

				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs(sampleUserData.Email).WillReturnRows(resources.SampleRowsGetUserByEmail(mocker, sampleUserData))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedRes := `{"code":"forbidden","message":"User is inactive"}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(api.Forbidden.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
		When("email not exist in database when login using jumpcloud", func() {
			It("should create a new user", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				// Simulate JumpCloud user info
				jumpcloudUser := jumpcloud.UserInfo{
					Email:     "<EMAIL>",
					FirstName: "New",
					LastName:  "User",
				}

				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"token": "tokenize",
					"type": "JUMPCLOUD"
				}`)

				// Simulate user not found in DB for req.Email
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrNoRows)

				// Set a fixed password hash for the new user
				newUser := resources.SampleDataForCreateUpdateUser()
				newUser.Email = jumpcloudUser.Email
				newUser.Password = "hashed-password"
				newUser.Name = jumpcloudUser.FirstName + " " + jumpcloudUser.LastName

				// Simulate user creation for jumpcloudUser
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateUser)).WithArgs(
					"New User", "<EMAIL>", sqlmock.AnyArg(), 1, sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				).WillReturnResult(sqlmock.NewResult(2, 1))

				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesUpdateUserExpiry)).WithArgs(sqlmock.AnyArg(), newUser.ID, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(2, 1))

				// get user by userid
				mocker.ExpectQuery(TestQueriesGetUserByUserID).WithArgs(sqlmock.AnyArg()).WillReturnRows(resources.SampleRowsGetUserByUserID(mocker, newUser))
				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetListElementPermission)).WithArgs(newUser.ID).WillReturnRows(resources.SampleRowsGetListElementPermission(mocker))

				auditTrail := resources.SampleDataUserLoginAuditTrail(newUser)
				mocker.ExpectExec(regexp.QuoteMeta(TestQueriesCreateAuditTrails)).WithArgs(
					auditTrail.Identifier, auditTrail.IdentifierType, auditTrail.Title, auditTrail.Description, auditTrail.ActivityType, auditTrail.ReferenceID, auditTrail.CreatedBy, sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(2, 1))

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				mockRedis.On("Delete", mock.Anything, mock.Anything).Return(true, nil)
				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				mockJumpcloud.On("VerifyEmail", mock.Anything, mock.Anything, mock.Anything).Return(jumpcloudUser, nil)

				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(200))
				Expect(res.Body.String).ShouldNot(BeNil())
			})
		})
	})
	Context("Error request", func() {
		When("error when fetching to DB", func() {
			It("return 500", func() {
				db, mocker, err := sqlmock.New()
				if err != nil {
					log.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
				}

				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "false-password",
					"type": "NATIVE"
				}`)

				// get user by email
				mocker.ExpectQuery(TestQueriesGetUserByEmail).WithArgs("<EMAIL>").WillReturnError(sql.ErrConnDone)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				expectedErr := `{
					"code": "internalServerError",
					"message": "sql: connection is already closed"
				}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedErr))
			})
		})
		When("error when get database handle", func() {
			It("return 500", func() {
				body := hcl.JSON(`{
					"email": "<EMAIL>",
					"password": "password",
					"type": "NATIVE"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(nil, errors.New("failed to connect DB"))

				expectedRes := `{"code":"internalServerError","message":"failed to connect DB"}`
				res, err := permissionManagemeneClient.Post(Login, body)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(res.StatusCode).Should(Equal(apiError.InternalServerError.HTTPStatusCode()))
				Expect(res.Body.String).Should(MatchJSON(expectedRes))
			})
		})
	})
})

package constants

import (
	accountDBMYServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceAPI "gitlab.super-id.net/bersama/core-banking/account-service/api"
)

var (
	// AllowedHoldCodes for block/unblock account
	AllowedHoldCodes = map[accountServiceAPI.ApplicableHoldcode]bool{
		accountServiceAPI.ApplicableHoldcode_WHOLE_BALANCE_HOLD: true,
		accountServiceAPI.ApplicableHoldcode_NO_DEBIT:           true,
	}

	// DbmyAllowedHoldCodes for block/unblock account
	DbmyAllowedHoldCodes = map[accountDBMYServiceAPI.ApplicableHoldcode]bool{
		accountDBMYServiceAPI.ApplicableHoldcode_ADVERSE_NEWS:          true,
		accountDBMYServiceAPI.ApplicableHoldcode_HITS:                  true,
		accountDBMYServiceAPI.ApplicableHoldcode_BANKRUPT:              true,
		accountDBMYServiceAPI.ApplicableHoldcode_DECEASED:              true,
		accountDBMYServiceAPI.ApplicableHoldcode_FRAUD_ACTOR:           true,
		accountDBMYServiceAPI.ApplicableHoldcode_FRAUD_VICTIM:          true,
		accountDBMYServiceAPI.ApplicableHoldcode_MENTAL_INCAPACITY:     true,
		accountDBMYServiceAPI.ApplicableHoldcode_MISSING_DEVICE:        true,
		accountDBMYServiceAPI.ApplicableHoldcode_MULE_ACCOUNT:          true,
		accountDBMYServiceAPI.ApplicableHoldcode_ORDER:                 true,
		accountDBMYServiceAPI.ApplicableHoldcode_KILL_SWITCH:           true,
		accountDBMYServiceAPI.ApplicableHoldcode_GARNISHEE_OR_CLAWBACK: true,
		accountDBMYServiceAPI.ApplicableHoldcode_BANK_RESTRICTED:       true,
	}
)

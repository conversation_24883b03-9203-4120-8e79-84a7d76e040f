package constants

const (
	// Ticket is activity type for ticket related
	Ticket = "TICKET"

	// Role is activity type for role related
	Role = "ROLE"

	// User is activity type for user related
	User = "USER"

	// Login is activity type for login
	Login = "LOGIN"

	// FeatureFlagConfig is activity type for feature flag related
	FeatureFlagConfig = "FEATURE_FLAG"

	// Permission is activity type for permission related
	Permission = "PERMISSION"

	// Logout is activity type for logout
	Logout = "LOGOUT"

	// DataSegregationConfig is activity type for data segregation
	DataSegregationConfig = "DATA_SEGREGATION"

	// CustomerSearchActivity is activity type for customer search
	CustomerSearchActivity = "CUSTOMER_SEARCH"
)

const (
	// TicketID is identifier type
	TicketID = "TICKET_ID"

	// AccountID is identifier type for account
	AccountID = "ACCOUNT_ID"

	// UserID is identifier type for user ID
	UserID = "USER_ID"

	// RoleID is identifier type for role ID
	RoleID = "ROLE_ID"

	// FeatureFlagKey is identifier type for feature flag key
	FeatureFlagKey = "FEATURE_FLAG_KEY"

	// PermissionID is identifier for permission ID
	PermissionID = "PERMISSION_ID"

	// ElementID is identifier type for element ID
	ElementID = "ELEMENT_ID"

	// ModuleID is identifier type for module ID
	ModuleID = "MODULE_ID"
)

// Audit trail event types
const (
	// SystemTaskFailed is event type for system task failure
	SystemTaskFailed = "SYSTEM_TASK_FAILED"

	// CaseStatusChanged is event type for case status change
	CaseStatusChanged = "CASE_STATUS_CHANGED"

	// CaseUpdated is event type for case update
	CaseUpdated = "CASE_UPDATED"

	// CaseCompleted is event type for case completion
	CaseCompleted = "CASE_COMPLETED"

	// CaseRejected is event type for case rejection
	CaseRejected = "CASE_REJECTED"

	// SystemTaskQueued is event type for system task queued
	SystemTaskQueued = "SYSTEM_TASK_QUEUED"

	// SystemTaskCompleted is event type for system task completion
	SystemTaskCompleted = "SYSTEM_TASK_COMPLETED"

	// CaseCreated is event type for case creation
	CaseCreated = "CASE_CREATED"

	// TaskAssigned is event type for task assignment
	TaskAssigned = "TASK_ASSIGNED"

	// TaskDeassigned is event type for task deassignment
	TaskDeassigned = "TASK_DEASSIGNED"

	// CaseStatusUpdated is event type for case status update
	CaseStatusUpdated = "CASE_STATUS_UPDATED"

	CaseCancelled = "CASE_CANCELLED"
)

package constants

// Hold code constants
const (
	// HoldCodeBankrupt Banking Ops Maker hold codes
	HoldCodeBankrupt          = "BANKRUPT"
	HoldCodeDeceased          = "DECEASED"
	HoldCodeMentalIncapacity  = "MENTAL_INCAPACITY"
	HoldCodeMissingDevice     = "MISSING_DEVICE"
	HoldCodeKillSwitch        = "KILL_SWITCH"
	HoldCodeGarnisheeClawback = "GARNISHEE/CLAWBACK"
	HoldCodeBankRestricted    = "BANK_RESTRICTED"

	// HoldCodeAdverseNews AML Ops Maker hold codes
	HoldCodeAdverseNews        = "ADVERSE_NEWS"
	HoldCodeAmlSanctionHits    = "AML/SANCTION HITS"
	HoldCodePoliceCourtSeizure = "POLICE/COURT/SEIZURE/LEA_ORDER"
	HoldCodeMuleAccount        = "MULE_ACCOUNT"

	// HoldCodeFraudActor Fraud Ops Maker hold codes
	HoldCodeFraudActor  = "FRAUD_ACTOR"
	HoldCodeFraudVictim = "FRAUD_VICTIM"
)

// TODO: Need to ensure role id are consistent for all env and countries, should we use name/code or move this to config instead?
// Role ID constants
const (
	RoleBankingOpsMaker    = 4
	RoleAmlOpsMaker        = 22
	RoleFraudOpsMaker      = 23
	RoleCollectionOpsMaker = 24
	RoleLendingOpsMaker    = 25
)

// RoleHoldCodesMap maps role names to their allowed hold codes
var RoleHoldCodesMap = map[int64][]string{
	RoleBankingOpsMaker: {
		HoldCodeBankrupt,
		HoldCodeDeceased,
		HoldCodeMentalIncapacity,
		HoldCodeMissingDevice,
		HoldCodeKillSwitch,
		HoldCodeGarnisheeClawback,
		HoldCodeBankRestricted,
	},
	RoleAmlOpsMaker: {
		HoldCodeAdverseNews,
		HoldCodeAmlSanctionHits,
		HoldCodePoliceCourtSeizure,
		HoldCodeMuleAccount,
	},
	RoleFraudOpsMaker: {
		HoldCodeFraudActor,
		HoldCodeFraudVictim,
		HoldCodeMuleAccount,
		HoldCodePoliceCourtSeizure,
		HoldCodeKillSwitch,
	},
	RoleCollectionOpsMaker: {
		HoldCodeDeceased,
		HoldCodeBankrupt,
		HoldCodeMentalIncapacity,
	},
	RoleLendingOpsMaker: {
		HoldCodeDeceased,
		HoldCodeBankrupt,
		HoldCodeMentalIncapacity,
	},
}

var UnblockAccountHoldCodesList = []string{
	HoldCodeBankrupt,
	HoldCodeDeceased,
	HoldCodeMentalIncapacity,
	HoldCodeMissingDevice,
	HoldCodeKillSwitch,
	HoldCodeGarnisheeClawback,
	HoldCodeBankRestricted,
	HoldCodeAdverseNews,
	HoldCodeAmlSanctionHits,
	HoldCodePoliceCourtSeizure,
	HoldCodeMuleAccount,
	HoldCodeFraudActor,
	HoldCodeFraudVictim,
}

const (
	HighPriority   = "High"
	MediumPriority = "Medium"
	LowPriority    = "Low"
)

const (
	BankingOpsDefaultRequesterValue    = "19"
	AMLOpsDefaultRequesterValue        = "12"
	FraudOpsDefaultRequesterValue      = "3"
	CollectionOpsDefaultRequesterValue = "22"
	LendingOpsDefaultRequesterValue    = "24"
)

// DefaultRequesterMap maps roles to the default requester value
var DefaultRequesterMap = map[int64]string{
	RoleBankingOpsMaker:    BankingOpsDefaultRequesterValue,
	RoleAmlOpsMaker:        AMLOpsDefaultRequesterValue,
	RoleFraudOpsMaker:      FraudOpsDefaultRequesterValue,
	RoleCollectionOpsMaker: CollectionOpsDefaultRequesterValue,
	RoleLendingOpsMaker:    LendingOpsDefaultRequesterValue,
}

// Ticket field IDs
const (
	FieldIDPriority           = "priority"
	FieldIDTicketRequestor    = "ticket_requestor"
	FieldIDDescription        = "description"
	FieldIDInputType          = "input_type"
	FieldIDInputValue         = "input_value"
	FieldIDHoldCodes          = "hold_codes"
	FieldIDInternalWatchlist  = "internal_watchlist_checkbox"
	FieldIDReferenceNumber    = "reference_number"
	FieldIDComment            = "comment"
	FieldIDUnblockAccountNote = "unblock_account_note"
)

// Ticket field names
const (
	FieldNamePriority           = "Priority"
	FieldNameSenderRequestor    = "Sender / Requestor"
	FieldNameDescription        = "Description"
	FieldNameInputType          = "Input Type"
	FieldNameInputValue         = "Input Value"
	FieldNameHoldCodesToApply   = "Hold Code(s) to Apply"
	FieldNameInternalWatchlist  = "Customer added to internal watchlist"
	FieldNameReferenceNumber    = "Reference Number"
	FieldNameComment            = "Comment"
	FieldNameUnblockAccountNote = "Unblock Account Note"
)

// Ticket field types
const (
	FieldTypeReadonly             = "readonly"
	FieldTypeSingleSelectDropdown = "single_select_dropdown"
	FieldTypeMultiSelectDropdown  = "multi_select_dropdown"
	FieldTypeTextArea             = "textarea"
	FieldTypeText                 = "text"
	FieldTypeCheckbox             = "checkbox"
	FieldTypeNote                 = "note"
)

// Input Value Name Field
const (
	AccountIDInputValueName   = "Account ID"
	CIFInputValueName         = "CIF"
	CustomerIDInputValueName  = "Customer ID"
	PhoneNumberInputValueName = "Phone Number"
)

const (
	AccountIDInputValue   = "accountID"
	CIFInputValue         = "cifNumber"
	CustomerIDInputValue  = "customerID"
	PhoneNumberInputValue = "phoneNumber"
)

var InputValueList = map[string]string{
	AccountIDInputValue:   AccountIDInputValueName,
	CIFInputValue:         CIFInputValueName,
	CustomerIDInputValue:  CustomerIDInputValueName,
	PhoneNumberInputValue: PhoneNumberInputValueName,
}

const (
	UnblockAccountNote         = "Checker approvals required will be based on selected hold codes."
	HoldCodeRequiredDependency = "hold_code"
)

// MandatoryInternalWatchlistHoldCodes Hold codes that require mandatory internal watchlist checkbox
var MandatoryInternalWatchlistHoldCodes = map[string]bool{
	HoldCodeAdverseNews:        true,
	HoldCodeAmlSanctionHits:    true,
	HoldCodeMuleAccount:        true,
	HoldCodePoliceCourtSeizure: true,
	HoldCodeFraudActor:         true,
}

// RequiresMandatoryInternalWatchlistCheckbox checks if a hold code requires the mandatory internal watchlist checkbox
func RequiresMandatoryInternalWatchlistCheckbox(holdCode string) bool {
	return MandatoryInternalWatchlistHoldCodes[holdCode]
}

package constants

const (
	// LogAuditTrailsLogTag ...
	LogAuditTrailsLogTag = "LogAuditTrailsLogTag"

	// GetLogAuditTrailLogsTag ...
	GetLogAuditTrailLogsTag = "GetLogAuditTrailLogsTag"

	// RequestUnlinkAccountValidationLogTag ...
	RequestUnlinkAccountValidationLogTag = "RequestUnlinkAccountValidationLogTag"

	// UnlinkAccountLogTag ...
	UnlinkAccountLogTag = "UnlinkAccountLogTag"

	// RequestUpdateCASAAccountStatusValidationLogTag ...
	RequestUpdateCASAAccountStatusValidationLogTag = "RequestUpdateCASAAccountStatusValidationLogTag"

	// UpdateCASAAccountStatusLogTag ...
	UpdateCASAAccountStatusLogTag = "UpdateCASAAccountStatusLogTag"

	// RequestTransferOnBehalfValidationLogTag ...
	RequestTransferOnBehalfValidationLogTag = "RequestTransferOnBehalfValidationLogTag"

	// RequestSendNotificationValidationLogTag ...
	RequestSendNotificationValidationLogTag = "RequestSendNotificationValidationLogTag"

	// RequestDeactivateLOCValidationLogTag ...
	RequestDeactivateLOCValidationLogTag = "RequestDeactivateLOCValidationLogTag"

	// TransferOnBehalfLogTag ...
	TransferOnBehalfLogTag = "TransferOnBehalfLogTag"

	// UnblockAccountLogTag ...
	UnblockAccountLogTag = "UnblockAccountLogTag"

	// BlockAccountLogTag ...
	BlockAccountLogTag = "BlockAccountLogTag"

	// PushInboxNotificationLogTag ...
	PushInboxNotificationLogTag = "PushInboxNotificationLogTag"

	// PushNotificationLogTag ...
	PushNotificationLogTag = "PushNotificationLogTag"

	// EmailNotificationLogTag ...
	EmailNotificationLogTag = "EmailNotificationLogTag"

	// SendNotificationLogTag ...
	SendNotificationLogTag = "SendNotificationLogTag"

	// DeactivateLOCLogTag ...
	DeactivateLOCLogTag = "DeactivateLOCLogTag"

	// GetListRoleLogTag ...
	GetListRoleLogTag = "GetListRoleLogTag"

	// LoginLogTag ...
	LoginLogTag = "LoginLogTag"

	// RefreshLoginLogTag ...
	RefreshLoginLogTag = "RefreshLoginLogTag"

	// LogoutLogTag ...
	LogoutLogTag = "LogoutLogTag"

	// ResetPasswordLogTag ...
	ResetPasswordLogTag = "ResetPasswordLogTag"

	// GetUserLogTag ...
	GetUserLogTag = "GetUserLogTag"

	// GetUsersLogTag ...
	GetUsersLogTag = "GetUsersLogTag"

	// GetUserPermissionsLogTag ...
	GetUserPermissionsLogTag = "GetUserPermissionsLogTag"

	// UpdateUserStatusLogTag ...
	UpdateUserStatusLogTag = "UpdateUserStatusLogTag"

	// CreateUserLogTag ...
	CreateUserLogTag = "CreateUserLogTag"

	// UpdateUserLogTag ...
	UpdateUserLogTag = "UpdateUserLogTag"

	// CreateRoleLogTag ...
	CreateRoleLogTag = "CreateRoleLogTag"

	// UpdateStatusRoleLogTag ...
	UpdateStatusRoleLogTag = "UpdateStatusRoleLogTag"

	// UpdateRoleLogTag ...
	UpdateRoleLogTag = "UpdateRoleLogTag"

	// GetRoleDetailLogTag ...
	GetRoleDetailLogTag = "GetRoleDetailLogTag"

	// GetOptionsLogTag ...
	GetOptionsLogTag = "GetOptionsLogTag"

	// GetDatabaseHandleLogTag ...
	GetDatabaseHandleLogTag = "GetDatabaseHandleLogTag"

	// CreateFeatureFlagLogTag ...
	CreateFeatureFlagLogTag = "CreateFeatureFlagLogTag"

	// UpdateFeatureFlagLogTag ...
	UpdateFeatureFlagLogTag = "UpdateFeatureFlagLogTag"

	// GetFeatureFlagLogTag ...
	GetFeatureFlagLogTag = "GetFeatureFlagLogTag"

	// DeleteFeatureFlagLogTag ...
	DeleteFeatureFlagLogTag = "DeleteFeatureFlagLogTag"

	// GetFeatureFlagListLogTag ...
	GetFeatureFlagListLogTag = "GetFeatureFlagListLogTag"

	// GetPermissionListLogTag ...
	GetPermissionListLogTag = "GetPermissionListLogTag"

	// CreatePermissionLogTag ...
	CreatePermissionLogTag = "CreatePermissionLogTag"

	// UpdatePermissionLogTag ...
	UpdatePermissionLogTag = "UpdatePermissionLogTag"

	// CreateElementLogTag ...
	CreateElementLogTag = "CreateElementLogTag"

	// UpdateElementLogTag ...
	UpdateElementLogTag = "UpdateElementLogTag"

	// CreateModuleLogTag ...
	CreateModuleLogTag = "CreateModuleLogTag"

	// UpdateModuleLogTag ...
	UpdateModuleLogTag = "UpdateModuleLogTag"

	// CustomerSearchLogTag ...
	CustomerSearchLogTag = "CustomerSearchLogTag"

	// GetCustomersLogTag ...
	GetCustomersLogTag = "GetCustomersLogTag"

	// GetDataSegregationRolesLogTag ...
	GetDataSegregationRolesLogTag = "GetDataSegregationRolesLogTag"

	// GetDataSegregationLogTag ...
	GetDataSegregationLogTag = "GetDataSegregationLogTag"

	// UpdateDataSegregationLogTag ...
	UpdateDataSegregationLogTag = "UpdateDataSegregationLogTag"

	// CustomerSearchDataPointLogTag ...
	CustomerSearchDataPointLogTag = "CustomerSearchDataPointLogTag"

	// GetEventLogLogTag ...
	GetEventLogLogTag = "GetEventLogLogTag"

	// GetTicketLogTag ...
	GetTicketLogTag = "GetTicketLogTag"

	// JumpCloudLogTag ...
	JumpCloudLogTag = "JumpCloudLogTag"
	// TransactionHistoryLogTag ...
	TransactionHistoryLogTag = "TransactionHistoryLogTag"

	// CustomerJourneyExperiencePreferenceLogTag ...
	CustomerJourneyExperiencePreferenceLogTag = "CustomerJourneyExperiencePreferenceLogTag"

	// CustomerExperienceHTTPLogTag ...
	CustomerExperienceHTTPLogTag = "CustomerExperienceHTTPLogTag"

	// CreateAuditTrailTag ...
	CreateAuditTrailTag = "CreateAuditTrailTag"

	// CustomerSearchAccountLogTag ...
	CustomerSearchAccountLogTag = "CustomerSearchAccountLogTag"

	// UpdateCustomerDataLogTag ...
	UpdateCustomerDataLogTag = "UpdateCustomerDataLogTag"
)

const (
	// PayAuthZLogErrorPrefix ...
	PayAuthZLogErrorPrefix = "PayAuthZ Service error -"

	// PaymentOpsTrfLogErrorPrefix ...
	PaymentOpsTrfLogErrorPrefix = "PaymentOpsTrf Service error -"

	// AccountServiceLogErrorPrefix ...
	AccountServiceLogErrorPrefix = "Account Service error -"

	// CustomerExperienceLogErrorPrefix ...
	CustomerExperienceLogErrorPrefix = "Customer Experience Service error -"

	// CustomerJourneyExperienceLogErrorPrefix ...
	CustomerJourneyExperienceLogErrorPrefix = "Customer Journey Experience Service error -"

	// AmlServiceLogErrorPrefix ...
	AmlServiceLogErrorPrefix = "AML Service error -"

	// CustomerMasterLogErrorPrefix ...
	CustomerMasterLogErrorPrefix = "Customer Master Service error -"

	// ProductMasterLogErrorPrefix ...
	ProductMasterLogErrorPrefix = "Product Master Service error -"

	// TransactionHistoryLogErrorPrefix ...
	TransactionHistoryLogErrorPrefix = "Transaction History Service error -"

	// TransactionLimitLogErrorPrefix ...
	TransactionLimitLogErrorPrefix = "Transaction Limit Service error -"

	// PayAuthzLogErrorPrefix ...
	PayAuthzLogErrorPrefix = "Pay Authz Service error -"

	// CustomerJournalLogErrorPrefix ...
	CustomerJournalLogErrorPrefix = "Customer Journal Service error -"

	// GrabIDLogErrorPrefix ...
	GrabIDLogErrorPrefix = "Grab ID Service error -"

	// LoanAppLogErrorPrefix ...
	LoanAppLogErrorPrefix = "Loan App Service error -"

	// LoanExpLogErrorPrefix ...
	LoanExpLogErrorPrefix = "Loan Exp Service error -"

	// LoanCoreLogErrorPrefix ...
	LoanCoreLogErrorPrefix = "Loan Core Service error -"

	// VideoCallLogErrorPrefix ...
	VideoCallLogErrorPrefix = "Video Call Service error -"
)

const (
	// GrabIDLogPrefix ...
	GrabIDLogPrefix = "Grab ID Service "
)

// Kafka Log Tags
const (
	// LoanCoreKafkaTag kafka consumer and logic layer log tag
	LoanCoreKafkaTag = "kafka-loan-core-streams"

	// LendingActionStatusKafkaTag kafka consumer and logic layer log tag
	LendingActionStatusKafkaTag = "kafka-lending-action-status-streams"
)

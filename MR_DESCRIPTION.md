# Fix Cyclic Import Issue in Calendar Module Authentication

## 🎯 Objective

Resolve the cyclic import error that occurs when trying to add authentication to the calendar module's `UpdateCalendar` function. The issue was caused by the calendar module directly importing the permission management logic, creating a circular dependency.

## 🐛 Problem

When attempting to add authentication to the calendar module by importing `permissionManagementLogic`, the following error occurred:

```
Cyclic imports are not allowed
```

This happened because:
- Calendar module was trying to import permission management logic
- Both modules are initialized in the same application, creating a circular dependency

## 💡 Solution

Implemented a **dependency injection pattern** using interfaces and adapters to break the circular dependency:

### Architecture Changes

1. **Interface Segregation**: Created `IAuthenticator` interface in calendar module
2. **Adapter Pattern**: Implemented `AuthAdapter` to bridge calendar and permission management
3. **Dependency Injection**: Used the existing DI system to wire dependencies at runtime

### Key Components

- **Interface**: `module/calendar/logic/i_auth.go` - Defines authentication contract
- **Adapter**: `module/calendar/adapters/auth_adapter.go` - Implements interface using permission logic
- **Injection**: Updated server initialization to register authenticator

## 📁 Files Changed

### Modified Files
- `module/calendar/logic/update_calendar.go` - Removed direct import, use injected authenticator
- `module/calendar/logic/init.go` - Added authenticator dependency to CalendarImpl
- `server/serve.go` - Added adapter registration and import

### New Files
- `module/calendar/logic/i_auth.go` - Authentication interface definition
- `module/calendar/adapters/auth_adapter.go` - Adapter implementation

## 🔧 Technical Details

### Before (Problematic)
```go
// Direct import causing cyclic dependency
import permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"

// Direct usage
user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(...)
```

### After (Solution)
```go
// Interface-based dependency
type CalendarImpl struct {
    Authenticator IAuthenticator `inject:"authenticator"`
}

// Usage through interface
user, hasPerm, err := s.Authenticator.AuthenticateRequestByElementCode(...)
```

## ✅ Benefits

- **Eliminates Cyclic Imports**: No more circular dependency errors
- **Clean Architecture**: Follows dependency inversion principle
- **Testability**: Easy to mock authenticator for unit tests
- **Maintainability**: Clear separation of concerns
- **Minimal Impact**: Preserves existing functionality

## 🧪 Testing

### Manual Testing
- [ ] Calendar update operations work with authentication
- [ ] Permission validation functions correctly
- [ ] No cyclic import errors during build
- [ ] Server starts successfully

### Automated Testing
- [ ] Existing calendar tests pass
- [ ] Authentication logic tests pass
- [ ] Integration tests verify end-to-end functionality

## 🚀 Deployment Notes

### Prerequisites
- Ensure permission management module is initialized before calendar module
- Verify dependency injection container is properly configured

### Rollback Plan
If issues arise, the changes can be easily reverted by:
1. Restoring direct import in `update_calendar.go`
2. Removing new interface and adapter files
3. Reverting server initialization changes

## 📋 Checklist

- [x] Cyclic import issue resolved
- [x] Authentication functionality preserved
- [x] Code follows established patterns
- [x] Documentation updated
- [ ] Tests updated/added
- [ ] Code review completed
- [ ] QA testing passed

## 🔗 Related Issues

- Resolves: Authentication implementation blocked by cyclic imports
- Related: Calendar module security enhancement

## 📝 Additional Notes

This implementation follows the existing codebase patterns and maintains backward compatibility. The dependency injection approach is consistent with how other cross-module dependencies are handled in the application.

The solution is scalable and can be extended to other calendar operations that require authentication (CreateCalendar, DeleteCalendar, etc.) without additional architectural changes.

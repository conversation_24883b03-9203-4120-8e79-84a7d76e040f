package logic

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"fmt"
	"mime"
	"strings"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CreateDocument creates a document
func (p *process) CreateDocument(ctx context.Context, req *api.CreateDocumentRequest) (*api.CreateDocumentResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Authenticate the request
	user, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// Upload the document
	url, err := p.uploadDocument(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to upload document")
	}

	// Get the database master handle
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	tx, err := master.Begin()
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}
	// nolint:errcheck
	defer tx.Rollback()

	documentID, err := storage.CreateDocument(ctx, tx, makeCreateDocumentDTO(req, user.ID, url))
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "logic:failed to create document")
	}

	// nolint:errcheck,gosec
	tx.Commit()

	// Get Presign Url
	presignUrl, _ := p.S3Client.PreSignedGetURL(p.AppConfig.S3Config.BucketName, fmt.Sprintf("%s/%s", p.AppConfig.S3Config.Directory, req.Name), 10)

	return &api.CreateDocumentResponse{
		Id:         documentID,
		Url:        url,
		Name:       req.Name,
		PresignUrl: presignUrl,
	}, nil
}

func (p *process) generateDocumentBucketPath(documentName string) string {
	return fmt.Sprintf("%s/%s", p.AppConfig.S3Config.Directory, documentName)
}

func getFileExtensionFromFileName(name string) (string, error) {
	splittedOriginalFileName := strings.Split(name, ".")
	if len(splittedOriginalFileName) < 2 {
		return "", errorwrapper.Error(apiError.BadRequest, "failed to get file extension")
	}
	return splittedOriginalFileName[len(splittedOriginalFileName)-1], nil
}

func (p *process) uploadDocument(req *api.CreateDocumentRequest) (string, error) {
	// Upload the document
	fileExtension, err := getFileExtensionFromFileName(req.Name)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.BadRequest, "failed to get file extension")
	}

	// Extract base64 content if payload starts with data URI scheme
	payload := req.Payload
	if strings.HasPrefix(payload, "data:") {
		// Find the base64 data after the comma
		split := strings.Split(payload, ",")
		if len(split) != 2 {
			return "", errorwrapper.WrapError(err, apiError.BadRequest, "invalid base64 data URI format")
		}
		payload = split[1]
	}

	// Decode base64 content
	decodedData, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.BadRequest, "failed to decode base64 content")
	}

	err = p.S3Client.Upload(
		p.AppConfig.S3Config.BucketName,
		p.generateDocumentBucketPath(req.Name),
		bytes.NewReader(decodedData),
		s3client.WithContentTypeForUpload(mime.TypeByExtension(fmt.Sprintf(".%s", fileExtension))),
	)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "failed to upload document")
	}

	return fmt.Sprintf("%s/%s/%s", p.AppConfig.S3Config.BucketURL, p.AppConfig.S3Config.Directory, req.Name), nil
}

func makeCreateDocumentDTO(req *api.CreateDocumentRequest, userID int64, url string) *storage.DocumentDTO {
	return &storage.DocumentDTO{
		CreatedAt:   sql.NullTime{Time: time.Now(), Valid: true},
		CreatedBy:   sql.NullInt64{Int64: userID, Valid: true},
		Name:        req.Name,
		URL:         url,
		Description: sql.NullString{String: req.Description, Valid: req.Description != ""},
		Type:        sql.NullString{String: req.Type, Valid: req.Type != ""},
		TicketID:    sql.NullInt64{Int64: req.TicketID, Valid: req.TicketID != 0},
	}
}

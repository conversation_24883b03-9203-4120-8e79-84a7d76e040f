package logic

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	dbmyAccountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// UnblockAccountMY is the business logic for unblocking an account.
//
// nolint: gocyclo, funlen, dupl
func (p *process) UnblockAccountMY(ctx context.Context, req *UnblockAccountRequest) (*UnblockAccountResponse, error) {
	slog.FromContext(ctx).Info(unblockAccountLogTag, fmt.Sprintf("[DEBUG][MY] Received unblock account request from SQS: %v", req))
	if req == nil {
		return nil, errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	var res = &UnblockAccountResponse{}

	// fetch the applicable hold codes
	currentHoldCodes, err := p.getDbmyCurrentAccountHoldCodes(ctx, req.AccountID)
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "error fetching hold codes", slog.Error(err))
		return nil, errorwrapper.Error(apiError.BadRequest, "account is not blocked")
	}

	updatedHoldCodes, isValid := removeDbmyBlockAccountHoldCode(currentHoldCodes, req.HoldCodes)
	slog.FromContext(ctx).Info(unblockAccountLogTag, fmt.Sprintf("[DEBUG][MY] Current hold codes: %v, Updated hold codes: %v", currentHoldCodes, updatedHoldCodes))
	if !isValid {
		slog.FromContext(ctx).Info(unblockAccountLogTag, fmt.Sprintf("[DEBUG][MY] Account is not blocked: %v", updatedHoldCodes))
		return nil, errorwrapper.Error(apiError.BadRequest, "account is not blocked")
	}

	// send request to account service to update the hold codes to unblock the account
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderIdempotencyKey, req.IdempotencyKey)
	resUpdate, err := p.DbmyAccountServiceClient.UpdateCASAAccountParameters(ctx, &dbmyAccountServiceAPI.UpdateCASAAccountParametersRequest{
		AccountID: req.AccountID,
		ProductSpecificParameters: &dbmyAccountServiceAPI.CASAAccountParams{
			ApplicableHoldcodes: updatedHoldCodes,
		},
		UpdatedBy: req.UpdatedBy,
	})
	if err != nil {
		slog.FromContext(ctx).Error(unblockAccountLogTag, "error updating account", slog.Error(err))
		return &UnblockAccountResponse{QueueFeedback: QueueFeedback{NeedRequeue: true}}, errorwrapper.WrapError(err, apiError.Idem, "failed to update account")
	}

	res = &UnblockAccountResponse{
		AccountID:     resUpdate.AccountID,
		Status:        resUpdate.Status,
		FailureReason: resUpdate.FailureReason,
	}

	// send notification if required
	//if req.IsSendNotification {
	//	params, err := structToMap(UnblockAccountNotificationParams{
	//		UserName:       "", // FIXME
	//		AccountWording: "", // FIXME
	//		AccountName:    "", // FIXME
	//		AccountIDs:     "", // FIXME
	//		MessageDate:    time.Now().Format("2006-01-02 15:04:05"),
	//	})
	//	if err != nil {
	//		return nil, errorwrapper.Error(api.Idem, "failed to marshal notification params")
	//	}
	//
	//	// FIXME: send notification email
	//	p.SendNotification(ctx, SendNotificationAction{
	//		ActionType:       constants.UnblockAccountAction,
	//		SafeID:           req.AccountID,
	//		TicketID:         req.TicketID,
	//		NotificationType: api.SendNotificationType_EMAIL,
	//		Params:           params,
	//	})
	//
	//	// FIXME: send notification push inbox, fix params
	//	p.SendNotification(ctx, SendNotificationAction{
	//		ActionType:       constants.UnblockAccountAction,
	//		SafeID:           req.AccountID,
	//		TicketID:         req.TicketID,
	//		NotificationType: api.SendNotificationType_PUSH_INBOX,
	//		Params:           params,
	//	})
	//}

	return &UnblockAccountResponse{
		AccountID:     res.AccountID,
		Status:        res.Status,
		FailureReason: res.FailureReason,
	}, nil
}

// removeDbmyBlockAccountHoldCode will remove hold code based on request
// Second return will be true if the hold code is removed/found, false otherwise. Proceed only if second return is true.
func removeDbmyBlockAccountHoldCode(existing []dbmyAccountServiceAPI.ApplicableHoldcode, req []string) ([]dbmyAccountServiceAPI.ApplicableHoldcode, bool) {
	var updatedHoldCodes = make([]dbmyAccountServiceAPI.ApplicableHoldcode, 0)
	var reqMap = make(map[dbmyAccountServiceAPI.ApplicableHoldcode]bool)
	var expectedLen = len(existing) - len(req)

	// if existing hold codes is empty, will invalidate the request
	if len(existing) == 0 {
		return existing, false
	}

	// make map for faster lookup
	for _, code := range req {
		reqMap[dbmyAccountServiceAPI.ApplicableHoldcode(code)] = true
	}

	// the idea is forming a new slice with hold codes that are not in the request
	for _, holdCode := range existing {
		if !reqMap[holdCode] {
			updatedHoldCodes = append(updatedHoldCodes, holdCode)
		}
	}

	// check the validity of new hold codes by comparing the slice length
	if expectedLen != len(updatedHoldCodes) {
		return existing, false
	}

	return updatedHoldCodes, true
}

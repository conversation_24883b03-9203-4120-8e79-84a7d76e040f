package logic

import (
	"context"
	"database/sql"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

func (p *process) GetOptions(ctx context.Context, req *api.GetOptionsRequest) (*api.GetOptionsResponse, error) {
	// Authenticate the request
	_, err := jwt.ParseJWTStringWithClaims(servus.HeaderFromCtx(ctx).Get(constants.CtxAuthorization), p.AppConfig.TokenKey.AccessTokenKey)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}

	// validate the request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// Get the database master handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	resp := &api.GetOptionsResponse{}

	switch req.Type {
	case api.OptionType_modules:
		resp.Data, err = getOptionsForModules(ctx, db)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get modules option")
		}
	case api.OptionType_elements:
		resp.Data, err = getOptionsForElements(ctx, db)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get elements option")
		}
	case api.OptionType_permissions:
		resp.Data, err = getOptionsForPermissions(ctx, db)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get permissions option")
		}
	case api.OptionType_roles:
		resp.Data, err = getOptionsForRoles(ctx, db)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get roles option")
		}
	case api.OptionType_ticketFilter:
		options, err := getOptionsForTicketFilter(ctx, db)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket filter options")
		}
		resp.Data = *options
	case api.OptionType_reasons:
		resp.Data = getOptionsForReasons()
	case api.OptionType_actionedUsers:
		resp.Data, err = getOptionsForActionedUsers(ctx, db)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket filter options")
		}
	default:
		resp.Data = make([]api.Options, 0)
	}

	return resp, nil
}

func getOptionsForModules(ctx context.Context, db *sql.DB) ([]api.Options, error) {
	modules, err := storage.GetModules(ctx, db, false, nil)
	if err != nil {
		return nil, err
	}

	modulesResp := make([]api.Options, 0)
	if len(modules) > 0 {
		for _, mod := range modules {
			curMod := api.Options{
				Id:   mod.ID,
				Name: mod.Name,
			}
			modulesResp = append(modulesResp, curMod)
		}
	}

	return modulesResp, nil
}

func getOptionsForElements(ctx context.Context, db *sql.DB) ([]api.Options, error) {
	elements, err := storage.GetElements(ctx, db, nil)
	if err != nil {
		return nil, err
	}

	elementsResp := make([]api.Options, 0)
	if len(elements) > 0 {
		for _, elem := range elements {
			curElem := api.Options{
				Id:       elem.ID,
				Name:     elem.Name,
				ModuleId: elem.ModuleID,
			}
			elementsResp = append(elementsResp, curElem)
		}
	}

	return elementsResp, nil
}

func getOptionsForPermissions(ctx context.Context, db *sql.DB) ([]api.Options, error) {
	permissions, err := permissionManagementStorage.GetPermissions(ctx, db)
	if err != nil {
		return nil, err
	}

	permResp := make([]api.Options, 0)
	if len(permissions) > 0 {
		for _, perm := range permissions {
			curPerm := api.Options{
				Id:       perm.ID,
				Name:     perm.Name,
				ModuleId: perm.ModuleID,
			}
			permResp = append(permResp, curPerm)
		}
	}

	return permResp, nil
}

func getOptionsForRoles(ctx context.Context, db *sql.DB) ([]api.Options, error) {
	roles, err := permissionManagementStorage.GetAllActiveRoles(ctx, db)
	if err != nil {
		return nil, err
	}

	roleResp := make([]api.Options, 0)
	if len(roles) > 0 {
		for _, role := range roles {
			curPerm := api.Options{
				Id:   role.ID,
				Name: role.Name,
			}
			roleResp = append(roleResp, curPerm)
		}
	}

	return roleResp, nil
}

func getOptionsForTicketFilter(ctx context.Context, db *sql.DB) (*api.TicketFilterOptions, error) {
	modules, err := getOptionsForModules(ctx, db)
	if err != nil {
		return nil, err
	}

	elements, err := getOptionsForElements(ctx, db)
	if err != nil {
		return nil, err
	}

	sourceSystem := []api.Options{
		{Key: "ONEDASH", Name: "ONEDASH"},
		{Key: "AML_SERVICE", Name: "AML SERVICE"},
	}

	integrationStatus := []api.Options{
		{Key: "success", Name: "Success"},
		{Key: "failed", Name: "Failed"},
		{Key: "partiallyFailed", Name: "Partially Failed"},
		{Key: "inProgress", Name: "In Progress"},
	}

	return &api.TicketFilterOptions{
		Modules:           modules,
		Elements:          elements,
		SourceSystem:      sourceSystem,
		IntegrationStatus: integrationStatus,
		AssignTo:          []api.Options{},
	}, nil
}

func getOptionsForReasons() []api.Options {
	// TODO: use real data from database
	reasons := []api.Options{
		{Id: 1, Name: "Customer Initiated"},
		{Id: 2, Name: "Fraud"},
		{Id: 3, Name: "AML"},
		{Id: 4, Name: "Deceased"},
		{Id: 5, Name: "Mental Incapacitated"},
		{Id: 6, Name: "Bankrupt"},
		{Id: 8, Name: "Court Order"},
		{Id: 9, Name: "Police Order"},
		{Id: 10, Name: "Account Review"},
		{Id: 11, Name: "Other"},
		{Id: 12, Name: "Regulator Order"},
		{Id: 13, Name: "Tax Order"},
	}

	return reasons
}

func getOptionsForActionedUsers(ctx context.Context, db *sql.DB) ([]api.Options, error) {
	users, err := permissionManagementStorage.GetAllUserNames(ctx, db)
	if err != nil {
		return nil, err
	}

	usersOption := make([]api.Options, 0)
	if len(users) > 0 {
		for _, user := range users {
			curUser := api.Options{
				Id:   user.ID,
				Name: user.Name,
			}
			usersOption = append(usersOption, curUser)
		}
	}

	return usersOption, nil
}

package logic

import (
	"context"
	"database/sql"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// ActionContextParams represents the parameters of the determineTicketActions function.
type ActionContextParams struct {
	TicketDTO        *storage.TicketDTO
	UserID           int64
	TicketActionsMap map[string]int64
	TicketChain      []*storage.TicketChainDTO
	TicketHistories  []storage.TicketHistoryDTO
	DB               *sql.DB
}

// ProcessActionsAndRolesParams represents the parameters of the processActionsAndRoles function.
type ProcessActionsAndRolesParams struct {
	TicketDTO         *storage.TicketDTO
	UserRolesIds      []int64
	TicketActionsMap  map[string]int64
	TicketChain       []*storage.TicketChainDTO
	TicketHistories   []storage.TicketHistoryDTO
	ExistingMakerUser bool
	DB                *sql.DB
}

// CollectAllRequiredRolesParams represents the parameters of the collectAllRequiredRoles function.
type CollectAllRequiredRolesParams struct {
	TicketDTO        *storage.TicketDTO
	UserRolesIds     []int64
	TicketChain      []*storage.TicketChainDTO
	TicketHistories  []storage.TicketHistoryDTO
	RolesRequired    []int64
	RolesRequiredMap map[int64]bool
	DB               *sql.DB
}

// GetTicketByID ...
//
// nolint: funlen
func (p *process) GetTicketByID(ctx context.Context, req *api.GetTicketByIDRequest) (*api.GetTicketByIDResponse, error) {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, err
	}

	// get ticket by id
	ticketDTO, err := storage.GetTicketByID(ctx, db, req.Id)
	if err != nil {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("failed to get ticket by id: %v", err))
	}

	//Authenticate the request
	user, bitwiseValue, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestForElement(ctx, ticketDTO.ElementID, constants.BitwiseValueReadTicket)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// get ticket chain to determine possible action id
	ticketChain, err := storage.GetTicketChainByElementIDAndCurrentStatusID(ctx, db, ticketDTO.ElementID, ticketDTO.TicketStatusID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.ResourceNotFound, fmt.Sprintf("failed to get ticket chain by element id and current status id: %v", err))
	}
	ticketActionsMap := make(map[string]int64)
	for _, chain := range ticketChain {
		if bitwiseValue&chain.BitwiseRequired == chain.BitwiseRequired {
			ticketActionsMap[chain.ActionName] = chain.NextStatusID
		}
	}

	// Get ticket histories
	ticketHistories, err := storage.GetTicketHistoriesByTicketID(ctx, db, req.Id)
	if err != nil {
		return nil, err
	}

	actionContextParams := &ActionContextParams{
		TicketDTO:        ticketDTO,
		UserID:           user.ID,
		TicketActionsMap: ticketActionsMap,
		TicketChain:      ticketChain,
		TicketHistories:  ticketHistories,
		DB:               db,
	}

	ticketActions, rolesRequired, err := p.determineTicketActions(ctx, actionContextParams)
	if err != nil {
		return nil, err
	}

	// Get role names for required roles
	roleNames, err := p.getRoleNames(ctx, db, rolesRequired)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("failed to get role names: %v", err))
		return nil, err
	}

	resTicketHistories := make([]api.TicketHistory, 0)
	ticketComments := make([]api.TicketComment, 0)
	for _, ticketHistory := range ticketHistories {
		resTicketHistories = append(resTicketHistories, api.TicketHistory{
			Id:           ticketHistory.ID,
			TicketID:     ticketHistory.TicketID,
			Action:       ticketHistory.ActionName,
			Data:         ticketHistory.Data,
			CreatedAt:    utils.DateAsString(ticketHistory.CreatedAt.Time),
			CreatedBy:    ticketHistory.CreatedBy.Int64,
			Note:         ticketHistory.Note.String,
			PrevStatusID: ticketHistory.PrevStatusID,
			NextStatusID: ticketHistory.NextStatusID,
		})

		if ticketHistory.Note.Valid && ticketHistory.Note.String != "" {
			ticketComments = append(ticketComments, api.TicketComment{
				Id:                ticketHistory.ID,
				Comment:           ticketHistory.Note.String,
				Action:            ticketHistory.ActionName,
				CreatedAt:         utils.DateAsString(ticketHistory.CreatedAt.Time),
				CreatedBy:         ticketHistory.CreatedBy.Int64,
				CreatedByUserName: ticketHistory.CreatedByName.String,
			})
		}
	}

	// Get linked tickets
	var linkedTickets []api.LinkedTicket

	// If this ticket has a parent, fetch the parent ticket
	if ticketDTO.ParentTicketID.Valid && ticketDTO.ParentTicketID.Int64 != 0 {
		parentDTO, err := storage.GetTicketByID(ctx, db, ticketDTO.ParentTicketID.Int64)
		if err == nil {
			linkedTickets = append(linkedTickets, api.LinkedTicket{
				Id:              parentDTO.ID,
				CreatedAt:       utils.DateAsString(parentDTO.CreatedAt.Time),
				CreatedBy:       fmt.Sprint(parentDTO.CreatedBy.Int64),
				CaseCategory:    parentDTO.CaseCategory.String,
				CaseSubcategory: parentDTO.CaseSubcategory.String,
				ElementID:       parentDTO.ElementID,
				Relationship:    constants.TicketRelationshipParent,
			})
		}
	}

	// If this ticket is a parent, fetch all child tickets
	childConditions := []commonStorage.QueryCondition{
		commonStorage.EqualTo("parent_ticket_id", ticketDTO.ID),
	}
	childDTOs, err := storage.GetTicketList(ctx, db, childConditions)
	if err != nil {
		return nil, err
	}

	if len(childDTOs) > 0 {
		for _, child := range childDTOs {
			linkedTickets = append(linkedTickets, api.LinkedTicket{
				Id:              child.ID,
				ElementID:       child.ElementID,
				StatusID:        child.TicketStatusID,
				CreatedAt:       utils.DateAsString(child.CreatedAt.Time),
				CreatedBy:       fmt.Sprint(child.CreatedBy.Int64),
				CaseCategory:    child.CaseCategory.String,
				CaseSubcategory: child.CaseSubcategory.String,
				ParentTicketID:  child.ParentTicketID.Int64,
				Relationship:    constants.TicketRelationshipChild,
			})
		}
	}

	return &api.GetTicketByIDResponse{
		Ticket: &api.Ticket{
			Id:                  ticketDTO.ID,
			ElementID:           ticketDTO.ElementID,
			Data:                ticketDTO.Data,
			StatusID:            ticketDTO.TicketStatusID,
			CreatedAt:           utils.DateAsString(ticketDTO.CreatedAt.Time),
			UpdatedAt:           utils.DateAsString(ticketDTO.UpdatedAt.Time),
			CreatedBy:           fmt.Sprint(ticketDTO.CreatedBy.Int64),
			UpdatedBy:           fmt.Sprint(ticketDTO.UpdatedBy.Int64),
			PriorityID:          ticketDTO.PriorityID,
			Source:              api.TicketSource(ticketDTO.Source),
			DeadlineTime:        utils.DateAsString(ticketDTO.DeadlineTime.Time),
			AssigneeUserID:      ticketDTO.AssigneeUserID.Int64,
			AssigneeUserName:    ticketDTO.AssigneeUserName.String,
			TicketRequestorName: ticketDTO.TicketRequestorName.String,
			CaseCategory:        ticketDTO.CaseCategory.String,
			CaseSubcategory:     ticketDTO.CaseSubcategory.String,
			DomainID:            ticketDTO.DomainID.String,
			Channel:             ticketDTO.Channel.String,
			CustomerSegmentName: ticketDTO.CustomerSegmentName.String,
			TicketCloseDatetime: utils.DateAsString(ticketDTO.TicketCloseDatetime.Time),
			ParentTicketID:      ticketDTO.ParentTicketID.Int64,
		},
		Histories:            resTicketHistories,
		ActionsNextStatusMap: ticketActions,
		RolesRequired:        roleNames,
		LinkedTickets:        linkedTickets,
		Comments:             ticketComments, // add this field for internal use
	}, nil
}

func (p *process) determineTicketActions(ctx context.Context, params *ActionContextParams) (map[string]int64, []int64, error) {
	// Early return if ticket is assigned to someone else
	if p.isTicketAssignedToOtherUser(params.TicketDTO, params.UserID) {
		return make(map[string]int64), []int64{}, nil
	}

	// Check if user is existing maker
	existingMakerUser := p.isExistingMakerUser(params.TicketDTO, params.UserID, params.TicketHistories)

	// Get user's roles
	userRolesIds, err := p.getUserRolesIds(ctx, params.UserID)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("failed to get user's roles: %v", err))
		return make(map[string]int64), []int64{}, err
	}

	processActionsAndRolesParams := &ProcessActionsAndRolesParams{
		TicketDTO:         params.TicketDTO,
		UserRolesIds:      userRolesIds,
		TicketActionsMap:  params.TicketActionsMap,
		TicketChain:       params.TicketChain,
		TicketHistories:   params.TicketHistories,
		ExistingMakerUser: existingMakerUser,
		DB:                params.DB,
	}

	// Process actions and collect required roles
	filteredActions, rolesRequired := p.processActionsAndRoles(ctx, processActionsAndRolesParams)

	// Handle unassigned ticket logic
	if p.shouldHandleUnassignedTicket(params.TicketDTO, filteredActions) {
		return p.handleUnassignedTicket(params.TicketDTO, params.TicketActionsMap, rolesRequired)
	}

	// Handle assigned ticket logic
	if p.isTicketAssignedToUser(params.TicketDTO, params.UserID) {
		p.addDeassignActionIfNeeded(filteredActions, params.TicketActionsMap, params.TicketDTO)
	}

	return filteredActions, rolesRequired, nil
}

// isTicketAssignedToOtherUser checks if the ticket is assigned to someone other than the current user
func (p *process) isTicketAssignedToOtherUser(ticketDTO *storage.TicketDTO, userID int64) bool {
	return ticketDTO.AssigneeUserID.Int64 != 0 && ticketDTO.AssigneeUserID.Int64 != userID
}

// isExistingMakerUser checks if the user is an existing maker for the ticket
func (p *process) isExistingMakerUser(ticketDTO *storage.TicketDTO, userID int64, ticketHistories []storage.TicketHistoryDTO) bool {
	for _, ticketHistory := range ticketHistories {
		if (ticketDTO.TicketStatusID == constants.TicketStatusInProgressChecker || ticketDTO.TicketStatusID == constants.TicketStatusInProgressApprover) &&
			ticketHistory.CreatedBy.Int64 == userID && ticketHistory.ActionName == constants.ActionMakerSubmit {
			return true
		}
	}
	return false
}

// processActionsAndRoles filters actions based on permissions and collects required roles
func (p *process) processActionsAndRoles(ctx context.Context, params *ProcessActionsAndRolesParams) (map[string]int64, []int64) {
	// Create a map of action to chain for easy lookup
	chainMap := p.createChainMap(params.TicketChain)

	// Filter actions based on conditions and role
	filteredActions := make(map[string]int64)
	rolesRequiredMap := make(map[int64]bool)
	var rolesRequired []int64

	for action, nextStatusID := range params.TicketActionsMap {
		chain := chainMap[action]

		checkActionPermissionParams := &CheckActionPermissionParams{
			TicketDTO:       params.TicketDTO,
			UserRolesIds:    params.UserRolesIds,
			TicketChain:     chain,
			TicketHistories: params.TicketHistories,
			DB:              params.DB,
		}

		canAction, roleRequiredForAction := checkActionPermission(ctx, checkActionPermissionParams)
		if chain != nil && canAction && !params.ExistingMakerUser {
			filteredActions[action] = nextStatusID
		}

		// Add unique roles to the map and list
		rolesRequired = p.addUniqueRoles(rolesRequired, rolesRequiredMap, roleRequiredForAction)
	}

	// Handle case when no actions are available
	if len(params.TicketActionsMap) == 0 {
		collectAllRequiredRolesParams := &CollectAllRequiredRolesParams{
			TicketDTO:        params.TicketDTO,
			UserRolesIds:     params.UserRolesIds,
			TicketChain:      params.TicketChain,
			TicketHistories:  params.TicketHistories,
			RolesRequired:    rolesRequired,
			RolesRequiredMap: rolesRequiredMap,
			DB:               params.DB,
		}
		rolesRequired = p.collectAllRequiredRoles(ctx, collectAllRequiredRolesParams)
	}

	return filteredActions, rolesRequired
}

// createChainMap creates a map of action names to chain DTOs for easy lookup
func (p *process) createChainMap(ticketChain []*storage.TicketChainDTO) map[string]*storage.TicketChainDTO {
	chainMap := make(map[string]*storage.TicketChainDTO)
	for _, chain := range ticketChain {
		chainMap[chain.ActionName] = chain
	}
	return chainMap
}

// addUniqueRoles adds unique role IDs to the rolesRequired slice
func (p *process) addUniqueRoles(rolesRequired []int64, rolesRequiredMap map[int64]bool, roleRequiredForAction []int64) []int64 {
	for _, roleID := range roleRequiredForAction {
		if !rolesRequiredMap[roleID] {
			rolesRequiredMap[roleID] = true
			rolesRequired = append(rolesRequired, roleID)
		}
	}
	return rolesRequired
}

// collectAllRequiredRoles collects all required roles when no actions are available
func (p *process) collectAllRequiredRoles(ctx context.Context, params *CollectAllRequiredRolesParams) []int64 {
	rolesRequired := params.RolesRequired
	for _, chain := range params.TicketChain {
		checkActionPermissionParams := &CheckActionPermissionParams{
			TicketDTO:       params.TicketDTO,
			UserRolesIds:    params.UserRolesIds,
			TicketChain:     chain,
			TicketHistories: params.TicketHistories,
			DB:              params.DB,
		}

		_, roleRequiredForAction := checkActionPermission(ctx, checkActionPermissionParams)
		rolesRequired = p.addUniqueRoles(rolesRequired, params.RolesRequiredMap, roleRequiredForAction)
	}
	return rolesRequired
}

// shouldHandleUnassignedTicket checks if we should handle unassigned ticket logic
func (p *process) shouldHandleUnassignedTicket(ticketDTO *storage.TicketDTO, filteredActions map[string]int64) bool {
	return ticketDTO.AssigneeUserID.Int64 == 0 && len(filteredActions) > 0
}

// handleUnassignedTicket handles the logic for unassigned tickets
func (p *process) handleUnassignedTicket(ticketDTO *storage.TicketDTO, ticketActionsMap map[string]int64, rolesRequired []int64) (map[string]int64, []int64, error) {
	// Return empty action if ticket is under execute_system
	if _, hasSystemExecute := ticketActionsMap[constants.ActionSystemExecute]; hasSystemExecute {
		return make(map[string]int64), []int64{}, nil
	}
	// Return assign to self action
	return map[string]int64{
		constants.ActionAssignSelf: ticketDTO.TicketStatusID,
	}, rolesRequired, nil
}

// isTicketAssignedToUser checks if the ticket is assigned to the current user
func (p *process) isTicketAssignedToUser(ticketDTO *storage.TicketDTO, userID int64) bool {
	return ticketDTO.AssigneeUserID.Int64 == userID
}

// addDeassignActionIfNeeded adds deassign action if the user doesn't have draft action
func (p *process) addDeassignActionIfNeeded(filteredActions map[string]int64, ticketActionsMap map[string]int64, ticketDTO *storage.TicketDTO) {
	if _, hasDraftAction := ticketActionsMap[constants.ActionMakerDraft]; !hasDraftAction {
		filteredActions[constants.ActionDeassignSelf] = ticketDTO.TicketStatusID
	}
}

func (p *process) getUserRolesIds(ctx context.Context, userID int64) ([]int64, error) {
	// Get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get user roles from database
	roles, err := permissionManagementStorage.GetUserRole(ctx, db, userID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get user roles")
	}

	// Convert role DTOs to role ids
	roleIds := make([]int64, 0, len(roles))
	for _, role := range roles {
		roleIds = append(roleIds, role.ID)
	}

	return roleIds, nil
}

// getRoleNames converts a slice of role IDs to their corresponding role names
func (p *process) getRoleNames(ctx context.Context, db *sql.DB, roleIDs []int64) ([]string, error) {
	if len(roleIDs) == 0 {
		return []string{}, nil
	}

	// Fetch all roles using roleIDs
	roles, err := permissionManagementStorage.GetRolesByIDs(ctx, db, roleIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get roles by ids: %w", err)
	}

	roleNames := make([]string, len(roles))
	for i, role := range roles {
		roleNames[i] = role.Name
	}

	return roleNames, nil
}

package logic

import (
	"context"
	"strconv"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// GetTicketFields ...
func (p *process) GetTicketFields(ctx context.Context, req *api.GetTicketFieldsRequest) (*api.GetTicketFieldsResponse, error) {
	var fields []api.TicketField

	// Authenticate the request to get user information
	user, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequest(ctx)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	// get the database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// Get user roles
	userRoles, err := permissionManagementStorage.GetUserRole(ctx, db, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get user roles")
	}

	var element *storage.ElementDTO
	if req.ElementID != 0 {
		var getElementErr error
		element, getElementErr = storage.GetElementByID(ctx, db, req.ElementID)
		if getElementErr != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element")
		}

		// Get element priorities
		priorities, getPrioritiesErr := storage.GetElementPriorities(ctx, db, req.ElementID)
		if getPrioritiesErr != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get element priorities DB")
		}
		fields = appendPriorities(fields, element.Code, priorities)

		// Append Ticket Requestor field
		ticketRequesters, ticketRequesterErr := storage.GetTicketRequestorList(ctx, db, []commonStorage.QueryCondition{})
		if ticketRequesterErr != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get ticket requestors")
		}
		fields = appendTicketRequester(fields, ticketRequesters, userRoles)

		// Append description fields
		fields = appendDescription(fields)

		// Append Dynamic fields based on element
		fields = appendDynamicFields(fields, element.Code, userRoles)
	}

	return &api.GetTicketFieldsResponse{
		Fields: fields,
	}, nil
}

func appendTicketRequester(fields []api.TicketField, ticketRequestors []storage.TicketRequestorDTO, userRoles []*permissionManagementStorage.RoleDTO) []api.TicketField {
	var ticketFieldOption []api.TicketFieldOption

	for _, ticketRequestor := range ticketRequestors {
		ticketFieldOption = append(ticketFieldOption, api.TicketFieldOption{
			Label: ticketRequestor.Name,
			Value: strconv.FormatInt(ticketRequestor.ID, 10),
		})
	}

	// Determine default value based on userRoles and BlockAccountDefaultRequesterMap
	defaultValue := ""
	for _, role := range userRoles {
		if val, ok := constants.DefaultRequesterMap[role.ID]; ok {
			defaultValue = val
			break
		}
	}

	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDTicketRequestor,
		FieldName:    constants.FieldNameSenderRequestor,
		FieldType:    constants.FieldTypeSingleSelectDropdown,
		IsRequired:   true,
		DefaultValue: defaultValue,
		Options:      ticketFieldOption,
	})

	return fields
}

func appendPriorities(fields []api.TicketField, elementCode string, priorities []*storage.TicketPriorityDTO) []api.TicketField {
	var ticketFieldOption []api.TicketFieldOption

	for _, priority := range priorities {
		ticketFieldOption = append(ticketFieldOption, api.TicketFieldOption{
			Label: priority.Name,
			Value: strconv.FormatInt(priority.ID, 10),
		})
	}

	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDPriority,
		FieldName:    constants.FieldNamePriority,
		FieldType:    constants.FieldTypeSingleSelectDropdown,
		IsRequired:   true,
		DefaultValue: getPrioritiesDefaultValue(elementCode, priorities),
		Readonly:     true,
		Options:      ticketFieldOption,
	})

	return fields
}

func getPrioritiesDefaultValue(elementCode string, priorities []*storage.TicketPriorityDTO) string {
	var priority string

	for _, p := range priorities {
		switch elementCode {
		case string(constants.BlockAccount):
			if p.Name == constants.HighPriority {
				priority = strconv.FormatInt(p.ID, 10)
				break
			}
		case string(constants.UnblockAccount):
			if p.Name == constants.MediumPriority {
				priority = strconv.FormatInt(p.ID, 10)
				break
			}
		}
	}

	return priority
}

func appendDescription(fields []api.TicketField) []api.TicketField {
	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDDescription,
		FieldName:    constants.FieldNameDescription,
		FieldType:    constants.FieldTypeTextArea,
		IsRequired:   false,
		DefaultValue: "",
	})

	return fields
}

func appendDynamicFields(fields []api.TicketField, elementCode string, userRoles []*permissionManagementStorage.RoleDTO) []api.TicketField {
	switch elementCode {
	case string(constants.BlockAccount):
		fields = buildBlockAccountFields(fields, userRoles)
	case string(constants.UnblockAccount):
		fields = buildUnblockAccountFields(fields)
	}
	return fields
}

func buildBlockAccountFields(fields []api.TicketField, userRoles []*permissionManagementStorage.RoleDTO) []api.TicketField {
	fields = buildInputTypeField(fields)
	fields = buildInputValueField(fields)
	fields = buildHoldCodesFieldForBlockAccount(fields, userRoles)
	fields = appendInternalWatchlistCheckbox(fields)
	fields = appendReferenceNumber(fields)
	fields = appendComment(fields)
	return fields
}

func buildUnblockAccountFields(fields []api.TicketField) []api.TicketField {
	fields = buildInputTypeField(fields)
	fields = buildInputValueField(fields)
	fields = buildHoldCodesFieldForUnblockAccount(fields)
	fields = appendUnblockAccountNote(fields)
	fields = appendReferenceNumber(fields)
	fields = appendComment(fields)
	return fields
}

// buildHoldCodesFieldForBlockAccount returns the hold codes field for block account based on user roles
func buildHoldCodesFieldForBlockAccount(fields []api.TicketField, userRoles []*permissionManagementStorage.RoleDTO) []api.TicketField {
	holdCodes := getHoldCodesForUserRoles(userRoles)
	var ticketFieldOption []api.TicketHoldCodeFieldOption
	for _, holdCode := range holdCodes {
		ticketFieldOption = append(ticketFieldOption, api.TicketHoldCodeFieldOption{
			Label:                              holdCode,
			Value:                              holdCode,
			MandatoryInternalWatchlistCheckbox: constants.RequiresMandatoryInternalWatchlistCheckbox(holdCode),
		})
	}
	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDHoldCodes,
		FieldName:    constants.FieldNameHoldCodesToApply,
		FieldType:    constants.FieldTypeMultiSelectDropdown,
		IsRequired:   true,
		DefaultValue: "",
		Options:      ticketFieldOption,
	})
	return fields
}

// buildHoldCodesFieldForUnblockAccount returns the hold codes field for unblock account
func buildHoldCodesFieldForUnblockAccount(fields []api.TicketField) []api.TicketField {
	var ticketFieldOption []api.TicketFieldOption
	for _, holdCode := range constants.UnblockAccountHoldCodesList {
		ticketFieldOption = append(ticketFieldOption, api.TicketFieldOption{
			Label: holdCode,
			Value: holdCode,
		})
	}
	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDHoldCodes,
		FieldName:    constants.FieldNameHoldCodesToApply,
		FieldType:    constants.FieldTypeMultiSelectDropdown,
		IsRequired:   true,
		DefaultValue: "",
		Options:      ticketFieldOption,
	})
	return fields
}

// getHoldCodesForUserRoles returns the hold codes that the user can access based on their roles
func getHoldCodesForUserRoles(userRoles []*permissionManagementStorage.RoleDTO) []string {
	holdCodesMap := make(map[string]bool)
	var holdCodes []string

	for _, role := range userRoles {
		if codes, exists := constants.RoleHoldCodesMap[role.ID]; exists {
			for _, code := range codes {
				if !holdCodesMap[code] {
					holdCodesMap[code] = true
					holdCodes = append(holdCodes, code)
				}
			}
		}
	}
	return holdCodes
}

// buildInputTypeField returns the input value fields
func buildInputTypeField(fields []api.TicketField) []api.TicketField {
	var ticketFieldOption []api.TicketFieldOption
	for key, label := range constants.InputValueList {
		ticketFieldOption = append(ticketFieldOption, api.TicketFieldOption{
			Label: label,
			Value: key,
		})
	}
	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDInputType,
		FieldName:    constants.FieldNameInputType,
		FieldType:    constants.FieldTypeSingleSelectDropdown,
		IsRequired:   true,
		DefaultValue: "",
		Options:      ticketFieldOption,
	})
	return fields
}

// buildInputValueField returns the input value fields
func buildInputValueField(fields []api.TicketField) []api.TicketField {
	fields = append(fields, api.TicketField{
		FieldId:    constants.FieldIDInputValue,
		FieldName:  constants.FieldNameInputValue,
		FieldType:  constants.FieldTypeText,
		IsRequired: true,
	})
	return fields
}

func appendInternalWatchlistCheckbox(fields []api.TicketField) []api.TicketField {
	fields = append(fields, api.TicketField{
		FieldId:              constants.FieldIDInternalWatchlist,
		FieldName:            constants.FieldNameInternalWatchlist,
		FieldType:            constants.FieldTypeCheckbox,
		IsRequiredDependancy: constants.HoldCodeRequiredDependency,
	})
	return fields
}

func appendReferenceNumber(fields []api.TicketField) []api.TicketField {
	fields = append(fields, api.TicketField{
		FieldId:    constants.FieldIDReferenceNumber,
		FieldName:  constants.FieldNameReferenceNumber,
		FieldType:  constants.FieldTypeText,
		IsRequired: true,
	})
	return fields
}

func appendComment(fields []api.TicketField) []api.TicketField {
	fields = append(fields, api.TicketField{
		FieldId:    constants.FieldIDComment,
		FieldName:  constants.FieldNameComment,
		FieldType:  constants.FieldTypeTextArea,
		IsRequired: false,
	})
	return fields
}

func appendUnblockAccountNote(fields []api.TicketField) []api.TicketField {
	fields = append(fields, api.TicketField{
		FieldId:      constants.FieldIDUnblockAccountNote,
		FieldName:    constants.FieldNameUnblockAccountNote,
		FieldType:    constants.FieldTypeText,
		IsRequired:   false,
		DefaultValue: constants.UnblockAccountNote,
		Readonly:     true,
	})
	return fields
}

package logic

import (
	"context"
	"encoding/json"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	dbmyAccountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// BlockAccountMY is the business logic for blocking an account.
//
// nolint: gocyclo, funlen, dupl
func (p *process) BlockAccountMY(ctx context.Context, req *BlockAccountRequest) (*BlockAccountResponse, error) {
	slog.FromContext(ctx).Info(blockAccountLogTag, fmt.Sprintf("[DEBUG][MY] Received block account request from SQS: %v", req))
	if req == nil {
		return nil, errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	var res = &BlockAccountResponse{}

	// fetch the applicable hold codes
	currentHoldCodes, err := p.getDbmyCurrentAccountHoldCodes(ctx, req.AccountID)
	if err != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, "error fetching hold codes", slog.Error(err))
		return nil, errorwrapper.WrapError(err, apiError.Idem, "error fetching hold codes")
	}

	// add business validation, if account is already blocked then return error
	updatedHoldCodes, isAlreadyExist := addDbmyBlockAccountHoldCode(currentHoldCodes, req.HoldCodes)
	slog.FromContext(ctx).Info(blockAccountLogTag, fmt.Sprintf("[DEBUG][MY] Current hold codes: %v, Updated hold codes: %v", currentHoldCodes, updatedHoldCodes))
	if isAlreadyExist {
		slog.FromContext(ctx).Info(blockAccountLogTag, fmt.Sprintf("[DEBUG][MY] holdcodes isAlreadyExist: %v", isAlreadyExist))
		return nil, errorwrapper.Error(apiError.BadRequest, "account is already blocked")
	}

	// send request to block account
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderIdempotencyKey, req.IdempotencyKey)
	resUpdate, err := p.DbmyAccountServiceClient.UpdateCASAAccountParameters(ctx, &dbmyAccountServiceAPI.UpdateCASAAccountParametersRequest{
		AccountID: req.AccountID,
		ProductSpecificParameters: &dbmyAccountServiceAPI.CASAAccountParams{
			ApplicableHoldcodes: updatedHoldCodes,
		},
		UpdatedBy: req.UpdatedBy,
	})
	if err != nil {
		slog.FromContext(ctx).Info(blockAccountLogTag, fmt.Sprintf("[DEBUG][MY] UpdateCASAAccountParameters error: %v", err))
		return &BlockAccountResponse{QueueFeedback: QueueFeedback{NeedRequeue: true}}, errorwrapper.WrapError(err, apiError.Idem, "error updating account")
	}

	res = &BlockAccountResponse{
		AccountID:     resUpdate.AccountID,
		Status:        resUpdate.Status,
		FailureReason: resUpdate.FailureReason,
	}

	// send notification if required
	//if req.IsSendNotification {
	//	params, err := structToMap(BlockAccountNotificationParams{
	//		UserName:       "", // FIXME
	//		AccountWording: "", // FIXME
	//		AccountName:    "", // FIXME
	//		AccountIDs:     "", // FIXME
	//		MessageDate:    time.Now().Format("2006-01-02 15:04:05"),
	//	})
	//	if err != nil {
	//		return nil, errorwrapper.WrapError(err, api.Idem, "error marshalling notification params")
	//	}
	//
	//	// FIXME: send notification email
	//	p.SendNotification(ctx, SendNotificationAction{
	//		ActionType:       constants.BlockAccountAction,
	//		SafeID:           req.AccountID,
	//		TicketID:         req.TicketID,
	//		NotificationType: api.SendNotificationType_EMAIL,
	//		Params:           params,
	//	})
	//
	//	// FIXME: send notification push inbox, fix params
	//	p.SendNotification(ctx, SendNotificationAction{
	//		ActionType:       constants.BlockAccountAction,
	//		SafeID:           req.AccountID,
	//		TicketID:         req.TicketID,
	//		NotificationType: api.SendNotificationType_PUSH_INBOX,
	//		Params:           params,
	//	})
	//}

	return &BlockAccountResponse{
		AccountID:     res.AccountID,
		Status:        res.Status,
		FailureReason: res.FailureReason,
	}, nil
}

// getCurrentAccountHoldCodes is used to get the current account hold codes
func (p *process) getDbmyCurrentAccountHoldCodes(ctx context.Context, accountID string) ([]dbmyAccountServiceAPI.ApplicableHoldcode, error) {
	accountDetails, err := p.DbmyAccountServiceClient.GetAccountDetailsByAccountID(ctx, &dbmyAccountServiceAPI.GetAccountRequest{
		AccountID: accountID,
	})
	if err != nil {
		slog.FromContext(ctx).Error("logic.getCurrentAccountHoldCodes", fmt.Sprintf("error getting account details: %v", err.Error()))
		return nil, errorwrapper.GetHTTPErrorResponse(err, "Failed to get account details")
	}

	str, ok := accountDetails.Account.ProductSpecificParameters[applicableHoldcodesKey]
	if !ok {
		slog.FromContext(ctx).Error("logic.getCurrentAccountHoldCodes", "applicableHoldcodesKey not found")
		return nil, errorwrapper.Error(apiError.InternalServerError, "Failed to get hold codes, invalid response key applicableHoldcodesKey not found")
	}

	var codes []dbmyAccountServiceAPI.ApplicableHoldcode
	err = json.Unmarshal([]byte(str), &codes)
	if err != nil {
		slog.FromContext(ctx).Error("logic.getCurrentAccountHoldCodes", fmt.Sprintf("error unmarshalling hold codes: %v", err.Error()))
		return nil, errorwrapper.Error(apiError.InternalServerError, "Failed to get hold codes, invalid unmarshall format")
	}

	return codes, nil
}

func addDbmyBlockAccountHoldCode(holdCodes []dbmyAccountServiceAPI.ApplicableHoldcode, req []string) ([]dbmyAccountServiceAPI.ApplicableHoldcode, bool) {
	var existing = make(map[dbmyAccountServiceAPI.ApplicableHoldcode]bool)
	var final = make([]dbmyAccountServiceAPI.ApplicableHoldcode, 0)
	// check if the hold code is already present & make map for faster lookup
	for _, code := range holdCodes {
		if constants.DbmyAllowedHoldCodes[code] {
			return holdCodes, true
		}
		existing[code] = true
	}
	final = append(final, holdCodes...)

	// add the hold codes from request
	for _, code := range req {
		// if account already have the hold code then will reject the request
		if existing[dbmyAccountServiceAPI.ApplicableHoldcode(code)] {
			return holdCodes, true
		}
		final = append(final, dbmyAccountServiceAPI.ApplicableHoldcode(code))
	}

	return final, false
}

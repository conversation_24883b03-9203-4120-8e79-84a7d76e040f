package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"slices"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"

	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// Condition represents a single condition for ticket chain actions
type Condition struct {
	Type     string      `json:"type"`     // e.g., "holdCode", "amount", "ticketType"
	Operator string      `json:"operator"` // e.g., "in", "gt", "lt", "eq", "between"
	Value    interface{} `json:"value"`    // Can be string, number, array, etc.
	RolesIDs []int64     `json:"rolesIDs"` // Roles that can perform the action
}

// TicketChainConditions represents all conditions for a ticket chain
type TicketChainConditions struct {
	Conditions []Condition `json:"conditions"`
}

// ConditionFunc Operator function type for condition evaluation
// Each function takes the ticket value and the condition value
// Returns true if the condition is met
type ConditionFunc func(value interface{}, condValue interface{}) bool

// Operator function implementations
func evalIn(value interface{}, condValue interface{}) bool {
	ticketValues, ok := value.([]interface{})
	if !ok {
		return false
	}
	conditionValues, ok := condValue.([]interface{})
	if !ok {
		return false
	}
	for _, ticketValue := range ticketValues {
		for _, cond := range conditionValues {
			// Convert both to float64 for comparison
			ticketNum, ticketOk := toFloat64(ticketValue)
			condNum, condOk := toFloat64(cond)
			if ticketOk && condOk && ticketNum == condNum {
				return true
			}
			// fallback to direct comparison for non-numeric types
			if ticketValue == cond {
				return true
			}
		}
	}
	return false
}

func toFloat64(val interface{}) (float64, bool) {
	switch v := val.(type) {
	case float64:
		return v, true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case float32:
		return float64(v), true
	default:
		return 0, false
	}
}

func evalGt(value interface{}, condValue interface{}) bool {
	val, ok := value.(float64)
	if !ok {
		return false
	}
	threshold, ok := condValue.(float64)
	if !ok {
		return false
	}
	return val > threshold
}

func evalLt(value interface{}, condValue interface{}) bool {
	val, ok := value.(float64)
	if !ok {
		return false
	}
	threshold, ok := condValue.(float64)
	if !ok {
		return false
	}
	return val < threshold
}

func evalBetween(value interface{}, condValue interface{}) bool {
	val, ok := value.(float64)
	if !ok {
		return false
	}
	rangeVal, ok := condValue.(map[string]interface{})
	if !ok {
		return false
	}
	minValue, ok := rangeVal["min"].(float64)
	if !ok {
		return false
	}
	maxValue, ok := rangeVal["max"].(float64)
	if !ok {
		return false
	}
	return val >= minValue && val <= maxValue
}

func evalEq(value interface{}, condValue interface{}) bool {
	return value == condValue
}

// Map of operator names to their corresponding functions
var operatorFuncs = map[string]ConditionFunc{
	"in":      evalIn,
	"gt":      evalGt,
	"lt":      evalLt,
	"between": evalBetween,
	"eq":      evalEq,
}

// checkActionPermission checks if a user can perform an action based on conditions and also returns the roles that can perform the action
func checkActionPermission(ctx context.Context, db *sql.DB, action string, ticketDTO *storage.TicketDTO, userRolesIds []int64, chain *storage.TicketChainDTO) (bool, []int64) {
	// If no conditions are set, allow the action
	if !chain.Conditions.Valid || chain.Conditions.String == "" {
		return true, []int64{}
	}

	var conditions TicketChainConditions
	if err := json.Unmarshal([]byte(chain.Conditions.String), &conditions); err != nil {
		return true, []int64{}
	}

	var requiredRoles []int64
	// Check each condition
	for _, condition := range conditions.Conditions {
		// Get the value to check based on condition type
		value := getValueFromTicketData(ctx, db, ticketDTO, condition.Type)
		if value == nil {
			continue
		}

		// Check if the condition is met
		if !evaluateCondition(value, condition) {
			continue
		}
		requiredRoles = condition.RolesIDs
		// Check if user has any of the required roles
		for _, role := range condition.RolesIDs {
			if hasRole(userRolesIds, role) {
				return true, requiredRoles
			}
		}

		return false, requiredRoles // Condition met but user doesn't have required role
	}

	return true, requiredRoles // No matching conditions found, allow the action
}

// getValueFromTicketData extracts the value for a condition type from ticket data
func getValueFromTicketData(ctx context.Context, db *sql.DB, ticketDTO *storage.TicketDTO, conditionType string) interface{} {
	if ticketDTO.Data == nil || ticketDTO.Data.Payload == nil {
		return nil
	}

	payload := ticketDTO.Data.Payload.(map[string]interface{})

	switch conditionType {
	case "HoldCodes":
		// Handle array of hold codes from payload
		if holdCodes, ok := payload["HoldCodes"].([]interface{}); ok {
			return holdCodes
		}
		return nil
	case "amount":
		return payload["amount"]
	case "ticketType":
		return payload["type"]
	case "makerRoles":
		histories, err := storage.GetTicketHistoriesByTicketID(ctx, db, ticketDTO.ID)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("failed to get ticket histories: %v", err))
			return nil
		}

		var makerSubmitUserRoleIDs []int64
		for _, history := range histories {
			if history.ActionName == constants.ActionMakerSubmit {
				userRoles, rolesErr := permissionManagementStorage.GetUserRole(ctx, db, history.CreatedBy.Int64)
				if rolesErr != nil {
					slog.FromContext(ctx).Error(constants.GetTicketLogTag, fmt.Sprintf("failed to get user roles: %v", err))
				}
				// Convert role DTOs to role ids
				for _, role := range userRoles {
					makerSubmitUserRoleIDs = append(makerSubmitUserRoleIDs, role.ID)
				}
			}
		}

		// Convert []int64 to []interface{} for evalIn compatibility
		result := make([]interface{}, len(makerSubmitUserRoleIDs))
		for i, v := range makerSubmitUserRoleIDs {
			result[i] = v
		}
		return result
	default:
		return nil
	}
}

// evaluateCondition checks if a value meets a condition using the operatorFuncs map
func evaluateCondition(value interface{}, condition Condition) bool {
	if fn, ok := operatorFuncs[condition.Operator]; ok {
		return fn(value, condition.Value)
	}
	return false
}

// hasRole checks if a user has a specific role
func hasRole(userRoles []int64, requiredRole int64) bool {
	return slices.Contains(userRoles, requiredRole)
}

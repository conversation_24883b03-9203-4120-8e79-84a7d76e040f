package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"slices"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CheckActionPermissionParams represents the parameters of the checkActionPermission function.
type CheckActionPermissionParams struct {
	TicketDTO       *storage.TicketDTO
	UserRolesIds    []int64
	TicketChain     *storage.TicketChainDTO
	TicketHistories []storage.TicketHistoryDTO
	DB              *sql.DB
}

// Condition represents a single condition for ticket chain actions
type Condition struct {
	Type     string      `json:"type"`     // e.g., "holdCode", "amount", "ticketType"
	Operator string      `json:"operator"` // e.g., "in", "gt", "lt", "eq", "between"
	Value    interface{} `json:"value"`    // Can be string, number, array, etc.
	RolesIDs []int64     `json:"rolesIDs"` // Roles that can perform the action
}

// TicketChainConditions represents all conditions for a ticket chain
type TicketChainConditions struct {
	Conditions []Condition `json:"conditions"`
}

// ConditionFunc Operator function type for condition evaluation
// Each function takes the ticket value and the condition value
// Returns true if the condition is met
type ConditionFunc func(value interface{}, condValue interface{}) bool

// Operator function implementations
func evalIn(value interface{}, condValue interface{}) bool {
	ticketValues, ok := value.([]interface{})
	if !ok {
		return false
	}
	conditionValues, ok := condValue.([]interface{})
	if !ok {
		return false
	}
	for _, ticketValue := range ticketValues {
		for _, cond := range conditionValues {
			// Convert both to float64 for comparison
			ticketNum, ticketOk := toFloat64(ticketValue)
			condNum, condOk := toFloat64(cond)
			if ticketOk && condOk && ticketNum == condNum {
				return true
			}
			// fallback to direct comparison for non-numeric types
			if ticketValue == cond {
				return true
			}
		}
	}
	return false
}

func toFloat64(val interface{}) (float64, bool) {
	switch v := val.(type) {
	case float64:
		return v, true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case float32:
		return float64(v), true
	default:
		return 0, false
	}
}

func evalGt(value interface{}, condValue interface{}) bool {
	val, ok := value.(float64)
	if !ok {
		return false
	}
	threshold, ok := condValue.(float64)
	if !ok {
		return false
	}
	return val > threshold
}

func evalLt(value interface{}, condValue interface{}) bool {
	val, ok := value.(float64)
	if !ok {
		return false
	}
	threshold, ok := condValue.(float64)
	if !ok {
		return false
	}
	return val < threshold
}

func evalBetween(value interface{}, condValue interface{}) bool {
	val, ok := value.(float64)
	if !ok {
		return false
	}
	rangeVal, ok := condValue.(map[string]interface{})
	if !ok {
		return false
	}
	minValue, ok := rangeVal["min"].(float64)
	if !ok {
		return false
	}
	maxValue, ok := rangeVal["max"].(float64)
	if !ok {
		return false
	}
	return val >= minValue && val <= maxValue
}

func evalEq(value interface{}, condValue interface{}) bool {
	return value == condValue
}

// Map of operator names to their corresponding functions
var operatorFuncs = map[string]ConditionFunc{
	"in":      evalIn,
	"gt":      evalGt,
	"lt":      evalLt,
	"between": evalBetween,
	"eq":      evalEq,
}

// checkActionPermission checks if a user can perform an action based on conditions and also returns the roles that can perform the action
func checkActionPermission(ctx context.Context, params *CheckActionPermissionParams) (bool, []int64) {
	// If no conditions are set, allow the action
	if !params.TicketChain.Conditions.Valid || params.TicketChain.Conditions.String == "" {
		return true, []int64{}
	}

	var conditions TicketChainConditions
	if err := json.Unmarshal([]byte(params.TicketChain.Conditions.String), &conditions); err != nil {
		slog.FromContext(ctx).Error(constants.TicketConditionLogTag, fmt.Sprintf("failed to unmarshal ticket chain conditions err %v", err))
		return false, []int64{}
	}

	var matchingConditionsExist bool
	var allRequiredRoles []int64

	// Check each condition
	for _, condition := range conditions.Conditions {
		value := getValueFromTicketData(ctx, params.TicketDTO, params.TicketHistories, condition.Type, params.DB)
		if value == nil || !evaluateCondition(value, condition) {
			continue // This condition doesn't apply to this ticket, check the next one.
		}

		// If we get here, it means a condition has matched the ticket data.
		matchingConditionsExist = true
		allRequiredRoles = append(allRequiredRoles, condition.RolesIDs...)

		// Check if the user has the role for this specific matched condition.
		for _, roleID := range condition.RolesIDs {
			if hasRole(params.UserRolesIds, roleID) {
				return true, condition.RolesIDs
			}
		}
	}

	if matchingConditionsExist {
		// At least one condition was met but the user didn't have the necessary role
		return false, allRequiredRoles
	}

	// No specific conditions matched the ticket. Allow by default.
	return true, []int64{}
}

// getValueFromTicketData extracts the value for a condition type from ticket data
func getValueFromTicketData(ctx context.Context, ticketDTO *storage.TicketDTO, ticketHistoriesDTO []storage.TicketHistoryDTO, conditionType string, db *sql.DB) interface{} {
	if ticketDTO.Data == nil || ticketDTO.Data.Payload == nil {
		return nil
	}

	payload := ticketDTO.Data.Payload.(map[string]interface{})

	switch conditionType {
	case constants.TicketConditionHoldCodes:
		// Handle array of hold codes from payload
		if holdCodes, ok := payload["HoldCodes"].([]interface{}); ok {
			return holdCodes
		}
		return nil
	case constants.TicketConditionAmount:
		return payload["amount"]
	case constants.TicketConditionTicketType:
		return payload["type"]
	case constants.TicketConditionMakerRoles:
		return getLastMakerRoles(ctx, db, ticketHistoriesDTO)
	default:
		return nil
	}
}

// evaluateCondition checks if a value meets a condition using the operatorFuncs map
func evaluateCondition(value interface{}, condition Condition) bool {
	if fn, ok := operatorFuncs[condition.Operator]; ok {
		return fn(value, condition.Value)
	}
	return false
}

// hasRole checks if a user has a specific role
func hasRole(userRoles []int64, requiredRole int64) bool {
	return slices.Contains(userRoles, requiredRole)
}

// getLastMakerRoles finds the last user to perform a "maker submit" action and returns their role IDs
func getLastMakerRoles(ctx context.Context, db *sql.DB, ticketHistoriesDTO []storage.TicketHistoryDTO) interface{} {
	var lastMakerUserID int64
	var makerFound bool

	// Get last maker user id
	for _, history := range ticketHistoriesDTO {
		if history.ActionName == constants.ActionMakerSubmit {
			lastMakerUserID = history.CreatedBy.Int64
			makerFound = true
		}
	}

	// Ensure a maker was actually found before querying the database.
	if !makerFound {
		slog.FromContext(ctx).Warn(constants.TicketConditionLogTag, "No maker submit action found in history")
		return []interface{}{} // Return empty if no maker
	}

	// Fetch roles for last maker user
	userRoles, rolesErr := permissionManagementStorage.GetUserRole(ctx, db, lastMakerUserID)
	if rolesErr != nil {
		slog.FromContext(ctx).Error(constants.TicketConditionLogTag, fmt.Sprintf("failed to get user roles for user %d: %v", lastMakerUserID, rolesErr))
		return nil // Return nil on error
	}

	// Convert role DTOs to a slice of role IDs
	makerSubmitUserRoleIDs := make([]int64, len(userRoles))
	for i, role := range userRoles {
		makerSubmitUserRoleIDs[i] = role.ID
	}

	// Convert []int64 to []interface{} for the evaluation function.
	result := make([]interface{}, len(makerSubmitUserRoleIDs))
	for i, v := range makerSubmitUserRoleIDs {
		result[i] = v
	}
	return result
}
